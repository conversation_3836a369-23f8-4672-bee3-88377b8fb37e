# Ultravox-s2s

This is an Voice-AI Jambonz application that connects to the Ultravox Realtime API. 

When building agents with Ultravox, you can extend your agents' capabilities by connecting them to external services and systems via [tools](https://docs.ultravox.ai/essentials/tools)—functions that agents can invoke to perform specific actions or retrieve information. These tools can be implemented as either client or server tools; we're covering both in this sample application. 
Read more about client vs server tools in the [Ultravox docs](https://docs.ultravox.ai/essentials/tools#server-vs-client-tools).

This application covers five scenarios:
1. create Booking using Ultravox serverTools. 
  - Bucht einen Termin für den Patienten

2. search Patient using Ultravox serverTools. 
  - Sucht nach einem Patienten in der Datenbank

3. get Available Appointments using Ultravox serverTools.
  - Ruft verfügbare Termine für den angegebenen Zeitraum ab.

4. call transfer using Ultravox serverTools. 
  - Leitet den Anruf an eine Kollegin weiter

5. collect PatientDetails using Ultravox serverTools. 
  - Erfasst und speichert die Daten eines neuen Patienten

The call transfer agent uses Jambonz to redirect the inbound call to a destination number of your choice.

## Prerequisites

- a [jambonz.cloud](https://jambonz.cloud/) account
- an [Ultravox](https://app.ultravox.ai/) account
- a carrier and virtual phone number of your choice

## Running instructions

### Set Environment Variables

Duplicate the *examples.env* file as *.env* and fill in your credentials as shown below.

```bash
ULTRAVOX_API_KEY=SoMe.ExAmpLeKeY
PORT=3000

# Required for Call Transfer
HUMAN_AGENT_NUMBER=+***********
HUMAN_AGENT_TRUNK=MyCarrier
HUMAN_AGENT_CALLERID=+***********

# Required for Server Tools
HTTP_BASE_URL=https://example.ngrok.io
JAMBONZ_ACCOUNT_SID=72c5c38f-test-test-test-aaa6be55e1b5
JAMBONZ_API_KEY=1cf2f4f4-stub-stub-stub-5bb4cb597c2a
JAMBONZ_BASE_URL=https://api.jambonz.cloud
```

| Environment Variable   | Value |
| :--------------------- | :---- |
| `ULTRAVOX_API_KEY`     | You can generate a new Ultravox API key under *Settings* in your [Ultravox account](https://app.ultravox.ai/settings/) |
| `PORT`                 | The port your Express server is listening at, you can use `3000` by default. |
| `HUMAN_AGENT_NUMBER`   | **WICHTIG:** Die Zieltelefonnummer im E.164-Format (z.B. `+***********`). Diese muss über Ihren konfigurierten Carrier erreichbar sein. |
| `HUMAN_AGENT_TRUNK`    | **WICHTIG:** Der exakte Name Ihres Carriers aus der Jambonz-Konfiguration. Muss identisch mit dem Carrier-Namen in Jambonz Cloud sein. |
| `HUMAN_AGENT_CALLERID` | Die Caller-ID für ausgehende Anrufe. Verwenden Sie Ihre virtuelle Nummer im E.164-Format, z.B. `+***********`. |
| `HTTP_BASE_URL`        | The URL you're serving this app up at. If running locally, you could use a tunneling service like [ngrok](https://ngrok.com/). In your terminal, run `ngrok http 3000` to get your base URL. E.g. `https://your-example-domain.ngrok.io` |
| `JAMBONZ_ACCOUNT_SID`  | Find this in your [jambonz.cloud](https://jambonz.cloud/) account under the *Account* tab. |
| `JAMBONZ_API_KEY`      | Generate a new API key in your [jambonz.cloud](https://jambonz.cloud/) account under the *Account* tab. |
| `JAMBONZ_BASE_URL`     | The base URL where you're running your Jambonz server. This will be `https://api.jambonz.cloud` if using jambonz.cloud. |

### Jambonz Setup

1. Create a Carrier entity in the Jambonz portal. [See docs](https://docs.jambonz.org/guides/using-the-jambonz-portal/basic-concepts/creating-carriers).
   - **WICHTIG:** Notieren Sie sich den exakten Namen des Carriers - dieser muss mit `HUMAN_AGENT_TRUNK` übereinstimmen
   - Stellen Sie sicher, dass der Carrier ausgehende Anrufe unterstützt
   - Testen Sie die Konnektivität zu Ihrem Carrier

2. Create a new Jambonz application in your portal under the [*Applications*](https://jambonz.cloud/internal/applications) tab.
Give it a name, then set both `Calling webhook` and `Call status webhook` values to the same URL:
    - **Für JASZ-AI mit Call Transfer:**  
    `ws://your-example-domain.ngrok.io/JASZ-AI`
    
    **Beispiel mit ngrok:**
    ```
    Calling webhook: ws://abc123.ngrok.io/JASZ-AI
    Call status webhook: ws://abc123.ngrok.io/JASZ-AI
    ```

3. After creating a carrier, you need to provision the phone number that you will be receiving calls on from that carrier. [See docs](https://docs.jambonz.org/guides/using-the-jambonz-portal/basic-concepts/creating-phone-numbers).
At the bottom of the page select the Jambonz application you just created to link your new virtual number to that application.

### Call Transfer Configuration Checklist

Bevor Sie das Call Transfer Feature testen, überprüfen Sie:

- [ ] **Carrier ist korrekt konfiguriert** und unterstützt ausgehende Anrufe
- [ ] **HUMAN_AGENT_NUMBER** ist im E.164-Format (`+***********`)
- [ ] **HUMAN_AGENT_TRUNK** stimmt exakt mit dem Carrier-Namen in Jambonz überein
- [ ] **HUMAN_AGENT_CALLERID** ist eine gültige Caller-ID, die Ihr Carrier erlaubt
- [ ] **HTTP_BASE_URL** ist über das Internet erreichbar (ngrok oder öffentliche Domain)
- [ ] **Jambonz Application** Webhooks zeigen auf die korrekte URL

### Run Your App

Ensure all environment variables are properly configured before starting the application. 
Navigate to the root of this project in your terminal, then run `npm install` and `npm start`.
Call your virtual number and test it out!

To switch between scenarios, update the `Calling webhook` and `Call status webhook` values to another URL, as explained in *step 2* of the *Jambonz Setup* section.

### Testing Call Transfer

Um das Call Transfer Feature zu testen:

1. **Rufen Sie Ihre virtuelle Nummer an**
2. **Lösen Sie einen Transfer aus** durch:
   - Explizite Bitte: "Können Sie mich mit einem Menschen verbinden?"
   - Medizinische Notfälle: "Ich habe starke Augenschmerzen"
   - Komplexe Anfragen: "Ich brauche eine ausführliche Beratung"

3. **Erwartetes Verhalten:**
   - KI sagt: "Einen Moment, ich verbinde Sie mit einem Kollegen..."
   - Anruf wird an `HUMAN_AGENT_NUMBER` weitergeleitet
   - Bei Problemen: Fallback-Nachricht und Auflegen

### Troubleshooting Call Transfer

#### Problem: AI sagt "ich verbinde Sie", aber es passiert nichts

**Symptom:** Die AI reagiert korrekt auf Transfer-Anfragen, aber der Anruf wird nicht weitergeleitet.

**Ursachen und Lösungen:**

1. **Carrier-Konfiguration (Peoplefone):**
   ```
   - Outbound Calls: AKTIVIERT
   - Destination Countries: Deutschland (+49) ERLAUBT
   - Allowed Caller-IDs: Ihre Hauptnummer hinzufügen
   - Cost Limits: Guthaben/Limits prüfen
   - Mobile Routing: +4917* aktiviert
   ```

2. **Jambonz Carrier-Test:**
   - Gehen Sie zu Jambonz Cloud → Carriers → Ihr Carrier
   - Klicken Sie auf "Test" um die Konnektivität zu prüfen
   - Prüfen Sie die "Outbound" Konfiguration

3. **Logging aktivieren:**
   ```bash
   # In Ihrer .env Datei:
   LOGLEVEL=debug
   
   # Neustart der App
   npm start
   ```

4. **Manuelle Tests:**
   - Testen Sie einen direkten ausgehenden Anruf über Jambonz
   - Prüfen Sie, ob `HUMAN_AGENT_NUMBER` korrekt erreichbar ist
   - Verwenden Sie zunächst eine Festnetznummer zum Testen

**Häufige Peoplefone-Probleme:**
- **Outbound nicht aktiviert:** Standard bei vielen Providern
- **Mobile-Nummern blockiert:** Viele Provider blockieren +4917x standardmäßig
- **Caller-ID nicht verifiziert:** `HUMAN_AGENT_CALLERID` muss bei Peoplefone registriert sein
- **Guthaben/Limits:** Prepaid-Guthaben oder Call-Limits erreicht

## Resources

- [Ultravox Documentation](https://docs.ultravox.ai)
  - [tools](https://docs.ultravox.ai/essentials/tools) in Ultravox
- [Jambonz Documentation](https://docs.jambonz.org)
  - the ['llm'](https://docs.jambonz.org/verbs/verbs/llm) verb
  - the ['dial'](https://docs.jambonz.org/verbs/verbs/dial) verb
  - the ['sip_refer'](https://docs.jambonz.org/verbs/verbs/sip-refer) verb
  - step-by-step [guides](https://docs.jambonz.org/guides/telephony-integrations) for adding carriers to Jambonz

