#!/usr/bin/env python3
"""
Patientendaten CSV Import Script
Importiert neue Patientendaten in PostgreSQL und vermeidet Duplikate anhand der patient_id
"""

import pandas as pd
import psycopg2
from psycopg2.extras import RealDictCursor
import os
import sys
from datetime import datetime

# Datenbank-Konfiguration
DB_CONFIG = {
    'host': 'localhost',
    'database': 'your_database_name',  # Hier den richtigen Datenbanknamen eintragen
    'user': 'postgres',
    'password': 'your_password'  # Hier das richtige Passwort eintragen
}

# CSV-Datei Pfad
CSV_FILE = '/home/<USER>/JASZ-AI-Freeswitch/INSTRUCTIONS/patientenliste-311701.csv'

def connect_db():
    """Stellt Verbindung zur PostgreSQL-Datenbank her"""
    try:
        conn = psycopg2.connect(**DB_CONFIG)
        return conn
    except psycopg2.Error as e:
        print(f"Fehler bei der Datenbankverbindung: {e}")
        return None

def create_patients_table(conn):
    """Erstellt die Patienten-Tabelle falls sie nicht existiert"""
    create_table_sql = """
    CREATE TABLE IF NOT EXISTS patients (
        patient_id VARCHAR(20) PRIMARY KEY,
        last_name VARCHAR(100),
        first_name VARCHAR(100),
        date_of_birth DATE,
        phone VARCHAR(20),
        address VARCHAR(200),
        email VARCHAR(100),
        insurance_type VARCHAR(50),
        registration_date DATE,
        last_visit DATE,
        doctor VARCHAR(100),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
    """
    
    try:
        cursor = conn.cursor()
        cursor.execute(create_table_sql)
        conn.commit()
        print("✓ Patienten-Tabelle erfolgreich erstellt/überprüft")
        cursor.close()
        return True
    except psycopg2.Error as e:
        print(f"Fehler beim Erstellen der Tabelle: {e}")
        return False

def get_existing_patient_ids(conn):
    """Holt alle bereits vorhandenen Patient-IDs aus der Datenbank"""
    try:
        cursor = conn.cursor()
        cursor.execute("SELECT patient_id FROM patients")
        existing_ids = {row[0] for row in cursor.fetchall()}
        cursor.close()
        print(f"✓ {len(existing_ids)} bereits vorhandene Patienten gefunden")
        return existing_ids
    except psycopg2.Error as e:
        print(f"Fehler beim Abrufen vorhandener Patient-IDs: {e}")
        return set()

def parse_date(date_str):
    """Konvertiert Datum-String im Format DD.MM.YYYY zu Date-Objekt"""
    if pd.isna(date_str) or date_str == '':
        return None
    try:
        return datetime.strptime(str(date_str), '%d.%m.%Y').date()
    except ValueError:
        try:
            # Fallback für andere Datumsformate
            return datetime.strptime(str(date_str), '%Y-%m-%d').date()
        except ValueError:
            print(f"Warnung: Ungültiges Datumsformat: {date_str}")
            return None

def load_csv_data():
    """Lädt und bereinigt die CSV-Daten"""
    try:
        # CSV laden mit Semikolon als Trenner
        df = pd.read_csv(CSV_FILE, sep=';', encoding='utf-8')
        print(f"✓ CSV-Datei geladen: {len(df)} Datensätze")
        
        # Leere patient_id Zeilen entfernen
        df = df.dropna(subset=['patient_id'])
        df = df[df['patient_id'] != '']
        
        # Datumsspalten konvertieren
        date_columns = ['date_of_birth', 'registration_date', 'last_visit']
        for col in date_columns:
            if col in df.columns:
                df[col] = df[col].apply(parse_date)
        
        # NaN zu None konvertieren für PostgreSQL
        df = df.where(pd.notnull(df), None)
        
        print(f"✓ Daten bereinigt: {len(df)} gültige Datensätze")
        return df
        
    except Exception as e:
        print(f"Fehler beim Laden der CSV-Datei: {e}")
        return None

def insert_new_patients(conn, df, existing_ids):
    """Fügt nur neue Patienten in die Datenbank ein"""
    # Nur neue Patienten filtern
    new_patients = df[~df['patient_id'].astype(str).isin(existing_ids)]
    
    if len(new_patients) == 0:
        print("✓ Keine neuen Patienten zum Import gefunden")
        return 0
    
    print(f"→ {len(new_patients)} neue Patienten gefunden zum Import")
    
    insert_sql = """
    INSERT INTO patients (
        patient_id, last_name, first_name, date_of_birth, phone, 
        address, email, insurance_type, registration_date, last_visit, doctor
    ) VALUES (
        %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
    )
    """
    
    try:
        cursor = conn.cursor()
        imported_count = 0
        
        for _, row in new_patients.iterrows():
            try:
                cursor.execute(insert_sql, (
                    str(row['patient_id']),
                    row['last_name'],
                    row['first_name'],
                    row['date_of_birth'],
                    row['phone'],
                    row['address'],
                    row['email'],
                    row['insurance_type'],
                    row['registration_date'],
                    row['last_visit'],
                    row['doctor']
                ))
                imported_count += 1
                
                # Fortschritt anzeigen
                if imported_count % 100 == 0:
                    print(f"  → {imported_count} Patienten importiert...")
                    
            except psycopg2.Error as e:
                print(f"Fehler beim Importieren von Patient {row['patient_id']}: {e}")
                continue
        
        conn.commit()
        cursor.close()
        print(f"✓ {imported_count} neue Patienten erfolgreich importiert")
        return imported_count
        
    except psycopg2.Error as e:
        print(f"Fehler beim Import: {e}")
        conn.rollback()
        return 0

def main():
    """Haupt-Import-Funktion"""
    print("=== Patienten CSV Import ===")
    print(f"CSV-Datei: {CSV_FILE}")
    
    # Prüfen ob CSV-Datei existiert
    if not os.path.exists(CSV_FILE):
        print(f"Fehler: CSV-Datei nicht gefunden: {CSV_FILE}")
        return
    
    # Datenbankverbindung herstellen
    print("\n1. Datenbankverbindung herstellen...")
    conn = connect_db()
    if not conn:
        return
    
    # Tabelle erstellen/prüfen
    print("\n2. Datenbank-Tabelle prüfen...")
    if not create_patients_table(conn):
        conn.close()
        return
    
    # Vorhandene Patient-IDs abrufen
    print("\n3. Vorhandene Patienten prüfen...")
    existing_ids = get_existing_patient_ids(conn)
    
    # CSV-Daten laden
    print("\n4. CSV-Daten laden...")
    df = load_csv_data()
    if df is None:
        conn.close()
        return
    
    # Neue Patienten importieren
    print("\n5. Neue Patienten importieren...")
    imported_count = insert_new_patients(conn, df, existing_ids)
    
    # Abschluss
    conn.close()
    print(f"\n=== Import abgeschlossen ===")
    print(f"Gesamt-Datensätze in CSV: {len(df)}")
    print(f"Bereits vorhanden: {len(existing_ids)}")
    print(f"Neu importiert: {imported_count}")

if __name__ == "__main__":
    main() 