const systemPrompt = require('../prompts/system-prompt.js');
const service = ({logger, makeService}) => {
  const svc = makeService({path: '/JASZ-AI'});

  svc.on('session:new', (session, path) => {
    session.locals = { ...session.locals,
      transcripts: [],
      logger: logger.child({call_sid: session.call_sid})
    };
    session.locals.logger.info({session, path}, `new incoming call: ${session.call_sid}`);

    const apiKey = process.env.ULTRAVOX_API_KEY;
    session
      .on('/event', onEvent.bind(null, session))
      .on('/final', onFinal.bind(null, session))
      .on('close', onClose.bind(null, session))
      .on('error', onError.bind(null, session));

    if (!apiKey) {
      session.locals.logger.info('missing env ULTRAVOX_API_KEY, hanging up');
      session
        .hangup()
        .send();
    }
    else if (!process.env.HTTP_BASE_URL) {
      session.locals.logger.info('missing env HTTP_BASE_URL, hanging up');
      session
        .hangup()
        .send();
    }
    else {
      session
        .answer()
    .pause({length: 0.5}) // Kürzere Pause für bessere UX
        .llm({
          vendor: 'ultravox',
          auth: {
            apiKey
          },
          actionHook: '/final',
          eventHook: '/event',
          llmOptions: {
            systemPrompt: systemPrompt,
            firstSpeaker: 'FIRST_SPEAKER_AGENT',
            languageHint: "de",
            voice: '40d1df42-894e-42c9-b1f0-c4c767944a00', // Standard Ultravox voice
            temperature: 0.3,
            // VAD-Einstellungen vereinfacht
            vadSettings: {
              turnEndpointDelay: "1.5s"
            },
            initialMessages: [
              {
                role: 'MESSAGE_ROLE_USER',
                text: `The user is calling from ${session.from}.`
              }
            ],
            selectedTools: [
              // 1. TRANSFER CALL - Vereinfacht
              {
                temporaryTool: {
                  modelToolName: 'transferCall',
                  description: 'Leitet den Anruf an eine Kollegin weiter.',
                  staticParameters: [
                    {
                      name: 'call_sid',
                      location: 'PARAMETER_LOCATION_BODY',
                      value: session.call_sid
                    }
                  ],
                  http: {
                    baseUrlPattern: `${process.env.HTTP_BASE_URL}/api/transfer`,
                    httpMethod: 'POST',
                  }
                }
              },
              // Weitere Tools temporär entfernt für Debugging
            ],
            transcriptOptional: true,
          }
        })
        .hangup()
        .send();
    }
  });
};

const onFinal = async(session, evt) => {
  const {logger} = session.locals;
  logger.info({evt}, `LLM session ended with event: ${JSON.stringify(evt)}`);

  // Wenn das LLM anzeigt, dass ein Tool-Aufruf abgeschlossen wurde, wurde der Anruf wahrscheinlich weitergeleitet.
  // Wir sollten in diesem Fall nicht "Sitzung beendet" sagen und auflegen.
  if (evt.completion_reason === 'tool_call_completed') {
    logger.info('LLM tool call completed, assuming call was transferred by the external tool.');
    session.reply(); // Bestätige den actionHook
    return;
  }

  // Behandle LLM-spezifische Fehler
  if (['server failure', 'server error', 'llm_error'].includes(evt.completion_reason)) {
    if (evt.error && evt.error.code === 'rate_limit_exceeded') {
      let text = 'Entschuldigung, Sie haben Ihr Ratenlimit überschritten. ';
      const arr = evt.error.message && /try again in (\d+)/.exec(evt.error.message);
      if (arr) {
        text += `Bitte versuchen Sie es in ${arr[1]} Sekunden erneut.`;
      }
      session.say({text});
    } else if (evt.error && evt.error.message) {
      logger.error({error: evt.error}, 'LLM reported an error');
      session.say({text: 'Entschuldigung, es gab einen Fehler mit dem Sprachassistenten.'});
    } else {
      session.say({text: 'Entschuldigung, bei der Bearbeitung Ihrer Anfrage ist ein unerwarteter Fehler aufgetreten.'});
    }
    session.hangup();
  } else if (evt.completion_reason !== 'tool_call_error') {
    // Für andere Beendigungsgründe oder wenn keine spezifische Fehlerbehandlung oben zutraf.
    session.say({text: 'Vielen Dank für Ihren Anruf. Auf Wiederhören.'});
    session.hangup();
  }
  session.reply();
};

const onEvent = async(session, evt) => {
  const {logger} = session.locals;
  logger.info(`got eventHook: ${JSON.stringify(evt)}`);
};

const onClose = (session, code, reason) => {
  const {logger} = session.locals;
  logger.info({code, reason}, `session ${session.call_sid} closed`);
};

const onError = (session, err) => {
  const {logger} = session.locals;
  logger.error({err}, `session ${session.call_sid} received error`);
};

module.exports = service;
