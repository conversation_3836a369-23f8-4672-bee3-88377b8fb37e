const systemPrompt = require('../prompts/system-prompt.js');
const service = ({logger, makeService}) => {
  const svc = makeService({path: '/JASZ-AI'});

  svc.on('session:new', (session, path) => {
    session.locals = { ...session.locals,
      transcripts: [],
      logger: logger.child({
        call_sid: session.call_sid,
        component: 'voice-agent'
      })
    };

    // Strukturiertes Logging für neuen Anruf
    session.locals.logger.info({
      event: 'call_started',
      call_sid: session.call_sid,
      caller: {
        from: session.from,
        to: session.to,
        caller_name: session.caller_name
      },
      session_details: {
        direction: session.direction,
        trace_id: session.trace_id,
        account_sid: session.account_sid,
        application_sid: session.application_sid
      },
      path: path
    }, 'New incoming call started');

    const apiKey = process.env.ULTRAVOX_API_KEY;
    session
      .on('/event', onEvent.bind(null, session))
      .on('/final', onFinal.bind(null, session))
      .on('close', onClose.bind(null, session))
      .on('error', onError.bind(null, session));

    if (!apiKey) {
      session.locals.logger.info('missing env ULTRAVOX_API_KEY, hanging up');
      session
        .hangup()
        .send();
    }
    else if (!process.env.HTTP_BASE_URL) {
      session.locals.logger.info('missing env HTTP_BASE_URL, hanging up');
      session
        .hangup()
        .send();
    }
    else {
      session
        .answer()
    .pause({length: 0.5}) // Kürzere Pause für bessere UX
        .llm({
          vendor: 'ultravox',
          model: 'fixie-ai/ultravox',
          auth: {
            apiKey
          },
      actionHook: '/final',
          eventHook: '/event',
          llmOptions: {
        systemPrompt: systemPrompt,
            firstSpeaker: 'FIRST_SPEAKER_AGENT',
            model: 'fixie-ai/ultravox',
            languageHint: "de",
            voice: '40d1df42-894e-42c9-b1f0-c4c767944a00',
            temperature: 0.3,
            // VAD-Einstellungen hinzufügen
            vadSettings: {
              turnEndpointDelay: "2.5s",           // Längere Wartezeit vor Sprecherwechsel
              minimumInterruptionDuration: "1.2s", // Benutzer muss länger sprechen zum Unterbrechen
              minimumTurnDuration: "0.8s",         // Agent spricht mindestens 0.8s bevor Unterbrechung möglich
              frameActivationThreshold: 0.7        // Höherer Schwellenwert (weniger empfindlich auf Hintergrundgeräusche)
            },
            initialMessages: [
              {
                role: 'MESSAGE_ROLE_USER',
                text: `The user is calling from ${session.from}.`
              }
            ],
            selectedTools: [
              // 1. TRANSFER CALL
              {
                temporaryTool: {
                  modelToolName: 'transferCall',
                  description: 'Leitet den Anruf an eine Kollegin weiter. Verwende dieses Tool bei medizinischen Notfällen, komplexen Anfragen oder auf expliziten Wunsch des Anrufers.',
                  staticParameters: [
                    {
                      name: 'call_sid',
                      location: 'PARAMETER_LOCATION_BODY',
                      value: session.call_sid
                    }
                  ],
                  dynamicParameters: [
                    {
                      name: 'transferReason',
                      location: 'PARAMETER_LOCATION_BODY',
                      schema: {
                        type: 'string',
                        description: 'Grund für die Weiterleitung (z.B. medizinischer Notfall, komplexe Beratung, Wunsch des Anrufers)'
                      },
                      required: false
                    }
                  ],
                  http: {
                    baseUrlPattern: `${process.env.HTTP_BASE_URL}/api/transfer`,
                    httpMethod: 'POST',
                  }
                }
              },
              // 2. SWITCH CALL STAGE
              {
                temporaryTool: {
                  modelToolName: 'switchCallStage',
                  description: 'Übergibt den Anrufer an einen anderen Agenten',
                  dynamicParameters: [
                    {
                      name: 'terminGrund',
                      location: 'PARAMETER_LOCATION_BODY',
                      schema: {
                        type: 'string',
                        description: 'Grund für den Termin oder die Weiterleitung'
                      },
                      required: true
                    }
                  ],
                  http: {
                    baseUrlPattern: `${process.env.HTTP_BASE_URL}/api/tools/switchCallStage`,
                    httpMethod: 'POST',
                  }
                }
              },
              // 3. GET AVAILABLE SLOTS
              {
                temporaryTool: {
                  modelToolName: 'getAvailableAppointments',
                  description: 'Ruft verfügbare Termine für den angegebenen Zeitraum ab',
                  dynamicParameters: [
                    {
                      name: 'appointmentReason',
                      location: 'PARAMETER_LOCATION_BODY',
                      schema: {
                        type: 'string',
                        description: 'Grund für den Termin (z.B. allgemeineKontrolle, entzuendungenBeratung, kinder, etc.)'
                      },
                      required: true
                    },
                    {
                      name: 'startTime',
                      location: 'PARAMETER_LOCATION_BODY',
                      schema: {
                        type: 'string',
                        description: 'Startzeit für die Terminsuche im ISO-Format'
                      },
                      required: true
                    },
                    {
                      name: 'endTime',
                      location: 'PARAMETER_LOCATION_BODY',
                      schema: {
                        type: 'string',
                        description: 'Endzeit für die Terminsuche im ISO-Format'
                      },
                      required: true
                    }
                  ],
                  http: {
                    baseUrlPattern: `${process.env.HTTP_BASE_URL}/api/tools/getAvailableAppointments`,
                    httpMethod: 'POST',
                  }
                }
              },
              // 4. CREATE BOOKING
              {
                temporaryTool: {
                  modelToolName: 'createBooking',
                  description: 'Bucht einen Termin für den Patienten',
                  dynamicParameters: [
                    {
                      name: 'firstName',
                      location: 'PARAMETER_LOCATION_BODY',
                      schema: {
                        type: 'string',
                        description: 'Vorname des Patienten'
                      },
                      required: true
                    },
                    {
                      name: 'lastName',
                      location: 'PARAMETER_LOCATION_BODY',
                      schema: {
                        type: 'string',
                        description: 'Nachname des Patienten'
                      },
                      required: true
                    },
                    {
                      name: 'date_of_birth',
                      location: 'PARAMETER_LOCATION_BODY',
                      schema: {
                        type: 'string',
                        description: 'Geburtsdatum des Patienten im Format DD.MM.YYYY'
                      },
                      required: true
                    },
                    {
                      name: 'phone',
                      location: 'PARAMETER_LOCATION_BODY',
                      schema: {
                        type: 'string',
                        description: 'Telefonnummer des Patienten'
                      },
                      required: true
                    },
                    {
                      name: 'insurance',
                      location: 'PARAMETER_LOCATION_BODY',
                      schema: {
                        type: 'string',
                        description: 'Krankenversicherung des Patienten'
                      },
                      required: false
                    },
                    {
                      name: 'address',
                      location: 'PARAMETER_LOCATION_BODY',
                      schema: {
                        type: 'string',
                        description: 'Adresse des Patienten'
                      },
                      required: false
                    },
                    {
                      name: 'email',
                      location: 'PARAMETER_LOCATION_BODY',
                      schema: {
                        type: 'string',
                        description: 'E-Mail-Adresse des Patienten'
                      },
                      required: false
                    },
                    {
                      name: 'startTime',
                      location: 'PARAMETER_LOCATION_BODY',
                      schema: {
                        type: 'string',
                        description: 'Startzeit des Termins im ISO-Format'
                      },
                      required: true
                    },
                    {
                      name: 'title',
                      location: 'PARAMETER_LOCATION_BODY',
                      schema: {
                        type: 'string',
                        description: 'Titel oder Beschreibung des Termins'
                      },
                      required: true
                    },
                    {
                      name: 'doctorID',
                      location: 'PARAMETER_LOCATION_BODY',
                      schema: {
                        type: 'string',
                        description: 'ID des Arztes für den Termin'
                      },
                      required: false
                    }
                  ],
                  http: {
                    baseUrlPattern: `${process.env.HTTP_BASE_URL}/api/tools/createBooking`,
                    httpMethod: 'POST',
                  }
                }
              },
              // 5. SEARCH PATIENT
              {
                temporaryTool: {
                  modelToolName: 'searchPatient',
                  description: 'Sucht nach einem Patienten in der Datenbank',
                  dynamicParameters: [
                    {
                      name: 'firstName',
                      location: 'PARAMETER_LOCATION_BODY',
                      schema: {
                        type: 'string',
                        description: 'Vorname des Patienten'
                      },
                      required: false
                    },
                    {
                      name: 'lastName',
                      location: 'PARAMETER_LOCATION_BODY',
                      schema: {
                        type: 'string',
                        description: 'Nachname des Patienten'
                      },
                      required: false
                    },
                    {
                      name: 'date_of_birth',
                      location: 'PARAMETER_LOCATION_BODY',
                      schema: {
                        type: 'string',
                        description: 'Geburtsdatum des Patienten im Format DD.MM.YYYY'
                      },
                      required: false
                    }
                  ],
                  http: {
                    baseUrlPattern: `${process.env.HTTP_BASE_URL}/api/tools/searchPatient`,
                    httpMethod: 'POST',
                  }
                }
              },
              // 6. QUERY CORPUS - RAG für Weiterleitungsregeln und Terminkriterien
              {
                builtinTool: {
                  toolName: 'queryCorpus',
                  parameterOverrides: {
                    corpus_id: process.env.ULTRAVOX_CORPUS_ID || '7c30a9da-d957-449c-944a-559ce7a98e89',
                    max_results: 5
                  }
                }
              },
              // 7. COLLECT PATIENT DETAILS
              {
                temporaryTool: {
                  modelToolName: 'collectPatientDetails',
                  description: 'Erfasst und speichert die Daten eines neuen Patienten',
                  dynamicParameters: [
                    {
                      name: 'firstName',
                      location: 'PARAMETER_LOCATION_BODY',
                      schema: {
                        type: 'string',
                        description: 'Vorname des Patienten'
                      },
                      required: true
                    },
                    {
                      name: 'lastName',
                      location: 'PARAMETER_LOCATION_BODY',
                      schema: {
                        type: 'string',
                        description: 'Nachname des Patienten'
                      },
                      required: true
                    },
                    {
                      name: 'date_of_birth',
                      location: 'PARAMETER_LOCATION_BODY',
                      schema: {
                        type: 'string',
                        description: 'Geburtsdatum des Patienten im Format DD.MM.YYYY'
                      },
                      required: true
                    },
                    {
                      name: 'phone',
                      location: 'PARAMETER_LOCATION_BODY',
                      schema: {
                        type: 'string',
                        description: 'Telefonnummer des Patienten'
                      },
                      required: true
                    },
                    {
                      name: 'insurance',
                      location: 'PARAMETER_LOCATION_BODY',
                      schema: {
                        type: 'string',
                        description: 'Krankenversicherung des Patienten'
                      },
                      required: false
                    },
                    {
                      name: 'address',
                      location: 'PARAMETER_LOCATION_BODY',
                      schema: {
                        type: 'string',
                        description: 'Adresse des Patienten'
                      },
                      required: false
                    },
                    {
                      name: 'email',
                      location: 'PARAMETER_LOCATION_BODY',
                      schema: {
                        type: 'string',
                        description: 'E-Mail-Adresse des Patienten'
                      },
                      required: false
                    }
                  ],
                  http: {
                    baseUrlPattern: `${process.env.HTTP_BASE_URL}/api/tools/collectPatientDetails`,
                    httpMethod: 'POST',
                  }
                }
              }
            ],
            transcriptOptional: true,
          }
        })
        .hangup()
        .send();
    }
  });
};

const onFinal = async(session, evt) => {
  const {logger} = session.locals;

  // Strukturiertes Logging für LLM-Session Ende
  logger.info({
    event: 'llm_session_ended',
    call_sid: session.call_sid,
    completion_reason: evt.completion_reason,
    call_status: evt.call_status,
    duration: evt.duration,
    error: evt.error || null,
    sip_details: {
      sip_status: evt.sip_status,
      sip_reason: evt.sip_reason
    }
  }, `LLM session ended: ${evt.completion_reason}`);

  // Wenn das LLM anzeigt, dass ein Tool-Aufruf abgeschlossen wurde, wurde der Anruf wahrscheinlich weitergeleitet.
  if (evt.completion_reason === 'tool_call_completed') {
    logger.info({
      event: 'tool_call_completed',
      call_sid: session.call_sid,
      action: 'call_transferred'
    }, 'LLM tool call completed, call was transferred');
    session.reply();
    return;
  }

  // Behandle LLM-spezifische Fehler
  if (['server failure', 'server error', 'llm_error'].includes(evt.completion_reason)) {
    if (evt.error && evt.error.code === 'rate_limit_exceeded') {
      logger.warn({
        event: 'rate_limit_exceeded',
        call_sid: session.call_sid,
        error_code: evt.error.code,
        error_message: evt.error.message
      }, 'Rate limit exceeded for LLM');

      let text = 'Entschuldigung, Sie haben Ihr Ratenlimit überschritten. ';
      const arr = evt.error.message && /try again in (\d+)/.exec(evt.error.message);
      if (arr) {
        text += `Bitte versuchen Sie es in ${arr[1]} Sekunden erneut.`;
      }
      session.say({text});
    } else if (evt.error && evt.error.message) {
      logger.error({
        event: 'llm_error',
        call_sid: session.call_sid,
        error_type: evt.error.type || 'unknown',
        error_message: evt.error.message,
        completion_reason: evt.completion_reason
      }, 'LLM reported an error');
      session.say({text: 'Entschuldigung, es gab einen Fehler mit dem Sprachassistenten.'});
    } else {
      logger.error({
        event: 'unexpected_error',
        call_sid: session.call_sid,
        completion_reason: evt.completion_reason
      }, 'Unexpected error during LLM processing');
      session.say({text: 'Entschuldigung, bei der Bearbeitung Ihrer Anfrage ist ein unerwarteter Fehler aufgetreten.'});
    }
    session.hangup();
  } else if (evt.completion_reason !== 'tool_call_error') {
    logger.info({
      event: 'call_ended_normally',
      call_sid: session.call_sid,
      completion_reason: evt.completion_reason
    }, 'Call ended normally');
    session.say({text: 'Vielen Dank für Ihren Anruf. Auf Wiederhören.'});
    session.hangup();
  }
  session.reply();
};

const onEvent = async(session, evt) => {
  const {logger} = session.locals;
  logger.info({
    event: 'webhook_event',
    call_sid: session.call_sid,
    event_type: evt.type || 'unknown',
    event_data: evt
  }, `Received webhook event: ${evt.type || 'unknown'}`);
};

const onClose = (session, code, reason) => {
  const {logger} = session.locals;
  logger.info({
    event: 'session_closed',
    call_sid: session.call_sid,
    close_code: code,
    close_reason: reason,
    session_duration: Date.now() - (session.startTime || Date.now())
  }, `Session closed with code ${code}`);
};

const onError = (session, err) => {
  const {logger} = session.locals;
  logger.error({
    event: 'session_error',
    call_sid: session.call_sid,
    error_type: err.constructor.name,
    error_message: err.message,
    error_stack: err.stack
  }, `Session error: ${err.message}`);
};

module.exports = service;
