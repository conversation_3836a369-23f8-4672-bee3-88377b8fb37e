const express = require('express');
const router = express.Router();
const toolsRouter = require('./tools');

// Füge die Tools-Routen ein
router.use('/tools', toolsRouter);

router.post('/transfer', (req, res) => {
  // Den Logger aus app.locals holen, der in app.js eingerichtet wurde
  const logger = req.app.locals.logger || console; // Fallback auf console, falls Logger nicht vorhanden
  const { call_sid, transferReason } = req.body; // call_sid und transferReason werden von der LLM-Tool-Konfiguration gesendet

  logger.info({ body: req.body, call_sid, transferReason }, `POST /api/transfer - Anfrage zur Anrufweiterleitung`);

  // Validierung der call_sid
  if (!call_sid) {
    logger.error('POST /api/transfer - Fehlende call_sid in der Anfrage');
    return res.status(400).json({ error: 'call_sid ist erforderlich für die Anrufweiterleitung.' });
  }

  // --- WICHTIG ---
  // <PERSON><PERSON> sicher, dass diese Umgebungsvariablen hier zugänglich sind.
  // Wenn diese API als separater Dienst läuft, benötigt sie eine eigene .env-Datei oder Konfiguration.
  // Wenn sie Teil derselben Node.js-App ist, sollte process.env funktionieren.
  const humanAgentNumber = process.env.HUMAN_AGENT_NUMBER;
  const humanAgentCallerId = process.env.HUMAN_AGENT_CALLERID;
  const humanAgentTrunk = process.env.HUMAN_AGENT_TRUNK;

  if (!humanAgentNumber || !humanAgentCallerId || !humanAgentTrunk) {
    logger.error({
      humanAgentNumber: !!humanAgentNumber,
      humanAgentCallerId: !!humanAgentCallerId,
      humanAgentTrunk: !!humanAgentTrunk
    }, 'Fehlende Umgebungsvariablen für /api/transfer');
    // Bei einem HTTP-Tool erwartet Jambonz eine Liste von Verben oder ein leeres 200 OK für Erfolg,
    // oder einen Fehlerstatuscode (z.B. 500) für einen Fehlschlag.
    // Wenn wir 500 zurückgeben, wird der 'tool_call_error' des LLM ausgelöst.
    return res.status(500).json({ error: 'Konfigurationsfehler: Agentendetails nicht festgelegt.' });
  }

  // Validierung der Telefonnummer (E.164 Format)
  if (!humanAgentNumber.match(/^\+[1-9]\d{1,14}$/)) {
    logger.error({ humanAgentNumber }, 'Ungültiges Telefonnummernformat für HUMAN_AGENT_NUMBER');
    return res.status(500).json({ error: 'Konfigurationsfehler: Ungültiges Telefonnummernformat.' });
  }

  // Personalisierte Nachricht basierend auf dem Weiterleitungsgrund
  const transferMessage = transferReason 
    ? `Einen Moment, ich verbinde Sie mit einer Kollegin für ${transferReason}.`
    : 'Einen Moment, ich verbinde Sie nun mit meiner Kollegin.';

  const jambonzVerbs = [
    {
      verb: 'say',
      text: transferMessage
    },
    {
      verb: 'dial',
      callerId: humanAgentCallerId,
      timeout: 30, // Ring-Timeout nach 30 Sekunden
      actionHook: '/api/transfer/dial-action', // Für besseres Logging des Transfer-Ergebnisses
      target: [
        {
          type: 'phone',
          number: humanAgentNumber,
          trunk: humanAgentTrunk
        }
      ]
    }
  ];

  logger.info({ verbs: jambonzVerbs, call_sid }, `POST /api/transfer - Antwort mit Verben zur Anrufweiterleitung`);
  res.status(200).json(jambonzVerbs);
});

// Action Hook für Dial-Ergebnis (für besseres Logging)
router.post('/transfer/dial-action', (req, res) => {
  const logger = req.app.locals.logger || console;
  logger.info({ body: req.body }, 'POST /api/transfer/dial-action - Transfer-Ergebnis');
  
  const { dial_status, dial_sip_status } = req.body;
  
  if (dial_status === 'completed') {
    logger.info({ dial_status, dial_sip_status }, 'Anruf erfolgreich weitergeleitet');
  } else {
    logger.warn({ dial_status, dial_sip_status }, 'Anrufweiterleitung fehlgeschlagen');
    // Fallback-Verhalten: Nachricht und Auflegen
    return res.status(200).json([
      {
        verb: 'say',
        text: 'Entschuldigung, die Weiterleitung an einen Kollegen ist momentan nicht möglich. Bitte rufen Sie später erneut an.'
      },
      {
        verb: 'hangup'
      }
    ]);
  }
  
  // Leere Antwort für erfolgreiche Weiterleitung
  res.status(200).json([]);
});

module.exports = router;