const express = require('express');
const router = express.Router();
const axios = require('axios');
const { Client } = require('pg');
const fuzz = require('fuzzball');
const soundex = require('soundex');

// PostgreSQL-Datenbankverbindung
const dbConfig = {
  host: 'localhost',
  port: 5432,
  database: 'jasz_patients_local',
  user: 'jaszadmin',
  password: 'jaszpassword'
};

// Hilfsfunktion für Datenbankverbindung
async function connectDB() {
  const client = new Client(dbConfig);
  await client.connect();
  return client;
}

// Deutsche Umlaut-Normalisierung
function normalizeGermanName(name) {
  if (!name) return '';
  
  return name
    .toLowerCase()
    .replace(/ä/g, 'ae')
    .replace(/ö/g, 'oe') 
    .replace(/ü/g, 'ue')
    .replace(/ß/g, 'ss')
    .replace(/[à<PERSON><PERSON><PERSON>å]/g, 'a')
    .replace(/[èéêë]/g, 'e')
    .replace(/[ìíîï]/g, 'i')
    .replace(/[òóôõø]/g, 'o')
    .replace(/[ùúûü]/g, 'u')
    .replace(/[ç]/g, 'c')
    .replace(/[ñ]/g, 'n')
    .trim();
}

// Intelligente Arztauswahl basierend auf Termingrund - ALLE passenden Ärzte
function selectAllDoctorsForAppointment(appointmentReason, logger) {
  try {
    // Alle verfügbaren Ärzte aus der env laden
    const availableDoctors = process.env.AVAILABLE_DOCTORS?.split(',') || [];
    
    if (availableDoctors.length === 0) {
      logger.warn('Keine Ärzte in AVAILABLE_DOCTORS definiert, verwende Fallback');
      return [];
    }

    // Verfügbare Ärzte für den gewünschten Termingrund finden
    const suitableDoctors = [];
    
    for (const doctorCode of availableDoctors) {
      const termingruende = process.env[`${doctorCode}_TERMINGRUENDE`];
      
      if (termingruende && termingruende.split(',').includes(appointmentReason)) {
        const doctorConfig = {
          code: doctorCode,
          apiKey: process.env[`${doctorCode}_CALCOM_API_KEY`],
          eventTypeId: process.env[`${doctorCode}_CALCOM_EVENT_TYPE_ID`],
          doctorName: process.env[`${doctorCode}_DOCTORS_NAME`],
          termingruende: termingruende.split(',')
        };
        
        // Prüfe ob alle erforderlichen Daten vorhanden sind
        if (doctorConfig.apiKey && doctorConfig.eventTypeId && doctorConfig.doctorName) {
          suitableDoctors.push(doctorConfig);
        } else {
          logger.warn(`Unvollständige Konfiguration für Arzt ${doctorCode}`);
        }
      }
    }

    if (suitableDoctors.length === 0) {
      logger.warn(`Kein Arzt für Termingrund '${appointmentReason}' gefunden`);
      return [];
    }

    logger.info({
      appointmentReason,
      selectedDoctors: suitableDoctors.map(d => ({ code: d.code, name: d.doctorName })),
      totalSuitableDoctors: suitableDoctors.length
    }, 'Alle passenden Ärzte für Termingrund gefunden');

    return suitableDoctors;
    
  } catch (error) {
    logger.error({ error: error.message }, 'Fehler bei der Arztauswahl');
    return [];
  }
}

// Einzelner Arzt für Fallback-Szenarien (Backward Compatibility)
function selectDoctorForAppointment(appointmentReason, logger) {
  const allDoctors = selectAllDoctorsForAppointment(appointmentReason, logger);
  return allDoctors.length > 0 ? allDoctors[0] : null;
}

// Erweiterte Namens-Ähnlichkeitsberechnung
function calculateNameSimilarity(inputName, dbName, logger) {
  if (!inputName || !dbName) return 0;
  
  // Normalisierte Versionen für besseren Vergleich
  const normalizedInput = normalizeGermanName(inputName);
  const normalizedDb = normalizeGermanName(dbName);
  
  // 1. Standard Fuzzy-Matching
  const fuzzyScore = fuzz.ratio(normalizedInput, normalizedDb);
  
  // 2. Phonetic Matching (Klang-Ähnlichkeit)
  const phoneticInput = soundex(normalizedInput);
  const phoneticDb = soundex(normalizedDb);
  const phoneticScore = phoneticInput === phoneticDb ? 100 : 0;
  
  // 3. Partial Matching (für zusammengesetzte Namen)
  const partialScore = fuzz.partial_ratio(normalizedInput, normalizedDb);
  
  // 4. Token-basiertes Matching (für Namen mit Leerzeichen)
  const tokenScore = fuzz.token_sort_ratio(normalizedInput, normalizedDb);
  
  // Gewichteter Score: Priorisiere exakte und phonetische Übereinstimmungen
  let finalScore = 0;
  
  // Exakte Übereinstimmung hat höchste Priorität
  if (normalizedInput === normalizedDb) {
    finalScore = 100;
  } else if (phoneticScore === 100) {
    // Phonetische Übereinstimmung ist sehr stark
    finalScore = Math.max(fuzzyScore, 90); // Mindestens 90% bei phonetischer Übereinstimmung
  } else {
    // Kombiniere verschiedene Matching-Methoden, priorisiere fuzzy
    finalScore = Math.max(
      fuzzyScore,
      partialScore * 0.8,  // Partial matching weniger gewichten
      tokenScore * 0.85    // Token matching weniger gewichten
    );
  }
  
  logger.info({
    inputName,
    dbName,
    normalizedInput,
    normalizedDb,
    phoneticInput,
    phoneticDb,
    scores: {
      fuzzy: fuzzyScore,
      phonetic: phoneticScore,
      partial: partialScore,
      token: tokenScore,
      final: Math.round(finalScore)
    }
  }, 'Name similarity calculation');
  
  return finalScore;
}

/**
 * Berechnet kombinierten Score aus Vor- und Nachname mit Priorisierung exakter Matches
 * @param {string} inputFirstName - Eingegebener Vorname
 * @param {string} inputLastName - Eingegebener Nachname  
 * @param {string} dbFirstName - Datenbankvorname
 * @param {string} dbLastName - Datenbanknachname
 * @returns {number} - Kombinierter Score (0-100)
 */
function calculateCombinedScore(inputFirstName, inputLastName, dbFirstName, dbLastName, logger) {
  // Normalisiere alle Namen für Vergleiche
  const normalizedInputFirst = normalizeGermanName(inputFirstName?.toLowerCase() || '');
  const normalizedInputLast = normalizeGermanName(inputLastName?.toLowerCase() || '');
  const normalizedDbFirst = normalizeGermanName(dbFirstName?.toLowerCase() || '');
  const normalizedDbLast = normalizeGermanName(dbLastName?.toLowerCase() || '');

  // ERSTE PRIORITÄT: Exakte Matches
  const exactFirstMatch = normalizedInputFirst === normalizedDbFirst;
  const exactLastMatch = normalizedInputLast === normalizedDbLast;
  
  if (exactFirstMatch && exactLastMatch) {
    logger.info({
      inputFirst: inputFirstName,
      inputLast: inputLastName,
      dbFirst: dbFirstName,
      dbLast: dbLastName,
      score: 100
    }, 'EXACT MATCH - beide Namen identisch');
    return 100;
  }

  // ZWEITE PRIORITÄT: Ein exakter Match + hoher Score beim anderen
  const firstNameScore = calculateNameSimilarity(inputFirstName, dbFirstName, logger);
  const lastNameScore = calculateNameSimilarity(inputLastName, dbLastName, logger);

  if (exactFirstMatch && lastNameScore >= 70) {
    const combinedScore = Math.round((100 + lastNameScore) / 2);
    logger.info({
      inputFirst: inputFirstName,
      inputLast: inputLastName,
      dbFirst: dbFirstName,
      dbLast: dbLastName,
      firstScore: 100,
      lastScore: lastNameScore,
      combinedScore: combinedScore
    }, 'PARTIAL EXACT MATCH - Vorname exakt');
    return combinedScore;
  }

  if (exactLastMatch && firstNameScore >= 70) {
    const combinedScore = Math.round((firstNameScore + 100) / 2);
    logger.info({
      inputFirst: inputFirstName,
      inputLast: inputLastName,
      dbFirst: dbFirstName,
      dbLast: dbLastName,
      firstScore: firstNameScore,
      lastScore: 100,
      combinedScore: combinedScore
    }, 'PARTIAL EXACT MATCH - Nachname exakt');
    return combinedScore;
  }

  // DRITTE PRIORITÄT: Fuzzy Matching mit gewichteter Kombination
  // Nachname ist wichtiger (60%) als Vorname (40%)
  const combinedScore = Math.round(
    (firstNameScore * 0.4) + (lastNameScore * 0.6)
  );

  logger.info({
    inputFirst: inputFirstName,
    inputLast: inputLastName,
    dbFirst: dbFirstName,
    dbLast: dbLastName,
    firstScore: firstNameScore,
    lastScore: lastNameScore,
    combinedScore: combinedScore
  }, 'FUZZY MATCH - gewichtete Kombination');

  return combinedScore;
}

// Intelligente Patientensuche mit Fuzzy-Matching
async function intelligentPatientSearch(firstName, lastName, dateOfBirth, client, logger) {
  // Schritt 1: Geburtsdatum als primärer Filter (falls vorhanden)
  let candidates = [];
  
  if (dateOfBirth) {
    // Erst alle Patienten mit passendem Geburtsdatum holen
    const query = 'SELECT * FROM patient WHERE date_of_birth = $1';
    const result = await client.query(query, [dateOfBirth]);
    candidates = result.rows;
    
    logger.info({ dateOfBirth, candidatesCount: candidates.length }, 'Candidates found by date of birth');
  } else {
    // Fallback: Breitere Suche für Fuzzy-Matching (ohne Geburtsdatum)
    let query = 'SELECT * FROM patient WHERE 1=1';
    const params = [];
    let paramIndex = 1;

    // Nur nach Nachnamen suchen (breiter), da Fuzzy-Matching dann die Feinabstimmung macht
    if (lastName) {
      // Verschiedene Suchstrategien kombinieren
      const normalizedLastName = normalizeGermanName(lastName);
      query += ` AND (
        LOWER(last_name) LIKE LOWER($${paramIndex}) OR 
        LOWER(last_name) LIKE LOWER($${paramIndex + 1}) OR
        LOWER(last_name) LIKE LOWER($${paramIndex + 2})
      )`;
      params.push(`%${lastName}%`);              // Original
      params.push(`%${normalizedLastName}%`);    // Normalisiert
      params.push(`%${lastName.substring(0, Math.min(3, lastName.length))}%`); // Erste 3 Buchstaben
      paramIndex += 3;
    }

    // Wenn kein Nachname, dann breitere Suche mit Vorname
    if (!lastName && firstName) {
      const normalizedFirstName = normalizeGermanName(firstName);
      query += ` AND (
        LOWER(first_name) LIKE LOWER($${paramIndex}) OR 
        LOWER(first_name) LIKE LOWER($${paramIndex + 1})
      )`;
      params.push(`%${firstName}%`);
      params.push(`%${normalizedFirstName}%`);
      paramIndex += 2;
    }

    query += ' LIMIT 100'; // Mehr Kandidaten für besseres Fuzzy-Matching

    const result = await client.query(query, params);
    candidates = result.rows;
    
    logger.info({ 
      candidatesCount: candidates.length,
      query: query,
      params: params 
    }, 'Candidates found by broad name search');
  }

  // Schritt 2: Intelligente Ähnlichkeitsberechnung mit Priorisierung exakter Matches
  let bestMatch = null;
  let highestScore = 0;
  
  for (const candidate of candidates) {
    // Verwende die neue intelligente Scoring-Funktion mit Priorisierung exakter Matches
    const combinedScore = calculateCombinedScore(
      firstName, 
      lastName, 
      candidate.first_name, 
      candidate.last_name, 
      logger
    );
    
    // Bester Treffer?
    if (combinedScore > highestScore) {
      highestScore = combinedScore;
      bestMatch = {
        ...candidate,
        fuzzyScore: combinedScore,
        // Zusätzliche Debug-Infos
        normalizedFirstName: normalizeGermanName(candidate.first_name),
        normalizedLastName: normalizeGermanName(candidate.last_name),
        phoneticFirstName: candidate.first_name ? soundex(normalizeGermanName(candidate.first_name)) : '',
        phoneticLastName: candidate.last_name ? soundex(normalizeGermanName(candidate.last_name)) : ''
      };
    }
  }
  
  logger.info({ 
    bestMatch: bestMatch ? {
      patient_id: bestMatch.patient_id,
      name: `${bestMatch.first_name} ${bestMatch.last_name}`,
      score: bestMatch.fuzzyScore
    } : null,
    highestScore 
  }, 'Fuzzy matching results');
  
  return { bestMatch, highestScore, totalCandidates: candidates.length };
}

// Dummy-Datenbank für Patienten und Termine (in einer realen Anwendung würde man eine echte Datenbank verwenden)
const patient = [];
const appointments = [];

// 1. TRANSFER CALL - bereits in index.js implementiert

// 2. SWITCH CALL STAGE
router.post('/switchCallStage', (req, res) => {
  const logger = getLogger(req);
  const { terminGrund } = req.body;

  logger.info({
    event: 'tool_call',
    tool_name: 'switchCallStage',
    parameters: { terminGrund },
    request_body: req.body,
    timestamp: new Date().toISOString()
  }, `Switch call stage requested for reason: ${terminGrund}`);

  // Hier könnte die Logik zum Wechsel des Call Stages implementiert werden
  // Für diese Beispielimplementierung senden wir einfach eine Erfolgsantwort zurück

  logger.info({
    event: 'tool_response',
    tool_name: 'switchCallStage',
    success: true,
    response_data: { terminGrund }
  }, `Call stage switched successfully`);

  res.status(200).json({
    success: true,
    message: `Call Stage gewechselt für Grund: ${terminGrund}`
  });
});

// 3. GET AVAILABLE SLOTS - Multi-Doctor-Search (alle passenden Ärzte parallel)
router.post('/getAvailableAppointments', async (req, res) => {
  const logger = getLogger(req);
  const { appointmentReason, startTime, endTime } = req.body;

  logger.info({
    event: 'tool_call',
    tool_name: 'getAvailableAppointments',
    parameters: { appointmentReason, startTime, endTime },
    request_body: req.body,
    timestamp: new Date().toISOString()
  }, `Multi-Doctor appointment search started for reason: ${appointmentReason}`);

  try {
    // Schritt 1: ALLE passenden Ärzte für den Termingrund finden
    const allSuitableDoctors = selectAllDoctorsForAppointment(appointmentReason, logger);
    
    if (allSuitableDoctors.length === 0) {
      throw new Error(`Kein Arzt für Termingrund '${appointmentReason}' verfügbar`);
    }

    logger.info({
      doctorCount: allSuitableDoctors.length,
      doctors: allSuitableDoctors.map(d => ({ code: d.code, name: d.doctorName }))
    }, 'Starte parallele Cal.com API Abfragen für alle passenden Ärzte');

    // Schritt 2: Parallel alle Ärzte bei Cal.com abfragen
    const allSlots = [];
    const doctorPromises = allSuitableDoctors.map(async (doctor) => {
      try {
        // Direkte Cal.com API Abfrage für jeden Arzt
        const calcomUrl = `https://api.cal.com/v1/slots`;
        const params = new URLSearchParams({
          apiKey: doctor.apiKey,
          eventTypeId: doctor.eventTypeId,
          startTime: startTime,
          endTime: endTime
        });

        logger.info({
          doctor: doctor.code,
          doctorName: doctor.doctorName,
          eventTypeId: doctor.eventTypeId,
          url: `${calcomUrl}?${params.toString()}`
        }, `Cal.com API Aufruf für ${doctor.code}`);

        const response = await axios.get(`${calcomUrl}?${params.toString()}`);
        
        if (response.data && response.data.slots) {
          // Slots von diesem Arzt verarbeiten
          const doctorSlots = [];
          
          // Cal.com gibt Slots gruppiert nach Datum zurück: { "2024-04-13": [{ "time": "..." }] }
          Object.entries(response.data.slots).forEach(([date, slots]) => {
            slots.forEach(slot => {
              doctorSlots.push({
                startTime: slot.time,
                endTime: new Date(new Date(slot.time).getTime() + 30 * 60000).toISOString(), // 30 Min Standard
                doctorID: doctor.code,
                doctorName: doctor.doctorName,
                calcomEventTypeId: doctor.eventTypeId,
                reason: appointmentReason,
                date: date
              });
            });
          });

          logger.info({
            doctor: doctor.code,
            slotsFound: doctorSlots.length
          }, `${doctorSlots.length} Slots von ${doctor.code} gefunden`);

          return doctorSlots;
        }
        
        logger.warn({
          doctor: doctor.code,
          response: response.data
        }, `Keine Slots von ${doctor.code} erhalten`);
        
        return [];
        
      } catch (error) {
        logger.error({
          doctor: doctor.code,
          error: error.message,
          response: error.response?.data
        }, `Fehler bei Cal.com API Aufruf für ${doctor.code}`);
        return [];
      }
    });

    // Warte auf alle API Calls
    const doctorResults = await Promise.all(doctorPromises);
    
    // Alle Slots sammeln
    doctorResults.forEach(slots => {
      allSlots.push(...slots);
    });

    // Schritt 3: Beste 3 Slots sortiert nach Zeit zurückgeben
    const sortedSlots = allSlots.sort((a, b) => new Date(a.startTime) - new Date(b.startTime));
    const best3Slots = sortedSlots.slice(0, 3);

    logger.info({
      totalSlotsFound: allSlots.length,
      doctorsQueried: allSuitableDoctors.length,
      returning: best3Slots.length
    }, 'Multi-Doctor-Search abgeschlossen');

    res.status(200).json({
      success: true,
      availableSlots: best3Slots,
      totalSlotsFound: allSlots.length,
      doctorsQueried: allSuitableDoctors.map(d => ({
        code: d.code,
        name: d.doctorName,
        termingruende: d.termingruende
      })),
      searchStats: {
        appointmentReason,
        startTime,
        endTime,
        doctorCount: allSuitableDoctors.length
      }
    });

  } catch (error) {
    logger.error({ error: error.message }, 'Fehler bei Multi-Doctor-Search');
    
    // Fallback: Generiere Beispieltermine mit verfügbaren Ärzten
    const allSuitableDoctors = selectAllDoctorsForAppointment(appointmentReason, logger);
    const availableSlots = [];
    const start = new Date(startTime || Date.now());
    const end = new Date(endTime || new Date(start).setDate(start.getDate() + 14));

    // Generiere 1 Termin pro verfügbarem Arzt, max 3 Termine
    const doctorsToUse = allSuitableDoctors.slice(0, 3);
    
    for (let i = 0; i < doctorsToUse.length; i++) {
      const doctor = doctorsToUse[i];
      const randomDate = new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()));
      randomDate.setHours(Math.floor(Math.random() * 10) + 8, 0, 0, 0);
      
      availableSlots.push({
        startTime: randomDate.toISOString(),
        endTime: new Date(randomDate.getTime() + 30 * 60000).toISOString(),
        doctorID: doctor.code,
        doctorName: doctor.doctorName,
        calcomEventTypeId: doctor.eventTypeId,
        reason: appointmentReason,
        fallback: true
      });
    }

    // Sortiere die Termine nach Datum
    availableSlots.sort((a, b) => new Date(a.startTime) - new Date(b.startTime));

    res.status(200).json({
      success: true,
      availableSlots,
      doctorsQueried: doctorsToUse.map(d => ({
        code: d.code,
        name: d.doctorName,
        termingruende: d.termingruende
      })),
      fallback: true,
      error: error.message
    });
  }
});

// 4. CREATE BOOKING - Mit intelligenter Arztauswahl
router.post('/createBooking', async (req, res) => {
  const logger = getLogger(req);
  const { 
    firstName, lastName, date_of_birth, phone, insurance, 
    address, email, startTime, title, doctorID 
  } = req.body;

  logger.info({ body: req.body }, `POST /api/tools/createBooking - Anfrage zur Terminbuchung`);

  try {
    // Schritt 1: Bestimme den Termingrund aus dem Titel oder verwende doctorID zur Arztsuche
    let selectedDoctor = null;
    
    if (doctorID) {
      // Versuche den Arzt anhand der doctorID zu finden
      const availableDoctors = process.env.AVAILABLE_DOCTORS?.split(',') || [];
      const doctorCode = availableDoctors.find(code => code === doctorID);
      
      if (doctorCode) {
        selectedDoctor = {
          code: doctorCode,
          apiKey: process.env[`${doctorCode}_CALCOM_API_KEY`],
          eventTypeId: process.env[`${doctorCode}_CALCOM_EVENT_TYPE_ID`],
          doctorName: process.env[`${doctorCode}_DOCTORS_NAME`],
          termingruende: process.env[`${doctorCode}_TERMINGRUENDE`]?.split(',') || []
        };
      }
    }
    
    if (!selectedDoctor) {
      // Fallback: Verwende den ersten verfügbaren Arzt
      const availableDoctors = process.env.AVAILABLE_DOCTORS?.split(',') || [];
      if (availableDoctors.length > 0) {
        const firstDoctor = availableDoctors[0];
        selectedDoctor = {
          code: firstDoctor,
          apiKey: process.env[`${firstDoctor}_CALCOM_API_KEY`],
          eventTypeId: process.env[`${firstDoctor}_CALCOM_EVENT_TYPE_ID`],
          doctorName: process.env[`${firstDoctor}_DOCTORS_NAME`],
          termingruende: process.env[`${firstDoctor}_TERMINGRUENDE`]?.split(',') || []
        };
      }
    }

    if (!selectedDoctor || !selectedDoctor.apiKey || !selectedDoctor.eventTypeId) {
      throw new Error('Keine gültige Arztkonfiguration gefunden');
    }

    logger.info({
      selectedDoctor: selectedDoctor.code,
      doctorName: selectedDoctor.doctorName,
      providedDoctorID: doctorID
    }, 'Arzt für Terminbuchung ausgewählt');

    // Schritt 2: Cal.com API mit arztspezifischen Daten aufrufen
    const apiUrl = `${req.protocol}://${req.get('host')}/api/cal/createBooking`;
    
    const response = await axios.post(apiUrl, {
      firstName,
      lastName,
      date_of_birth,
      phone,
      insurance,
      email,
      startTime,
      title,
      doctorID: selectedDoctor.code,
      // Arztspezifische Cal.com Parameter hinzufügen
      calcomApiKey: selectedDoctor.apiKey,
      calcomEventTypeId: selectedDoctor.eventTypeId,
      doctorName: selectedDoctor.doctorName
    });

    if (response.data && response.data.success) {
      res.status(200).json({
        success: true,
        booking: {
          ...response.data.booking,
          doctorInfo: {
            code: selectedDoctor.code,
            name: selectedDoctor.doctorName,
            eventTypeId: selectedDoctor.eventTypeId
          }
        }
      });
    } else {
      throw new Error('Terminbuchung fehlgeschlagen');
    }
  } catch (error) {
    logger.error({ error: error.message }, 'Fehler bei der Terminbuchung');
    
    // Fallback: Erstelle einen Dummy-Termin mit Arztinformationen
    const selectedDoctor = doctorID ? 
      { code: doctorID, name: `Arzt ${doctorID}` } : 
      { code: 'ML', name: 'Dr. med. Matthias Lenglinger' };

    const booking = {
      id: `booking-${Date.now()}`,
      patientName: `${firstName} ${lastName}`,
      date_of_birth,
      startTime,
      doctorID: selectedDoctor.code,
      doctorName: selectedDoctor.name,
      title: title || 'Routineuntersuchung',
      createdAt: new Date().toISOString(),
      fallback: true
    };

    res.status(200).json({
      success: true,
      booking
    });
  }
});

// 5. SEARCH PATIENT - Intelligente Suche mit Fuzzy-Matching
router.post('/searchPatient', async (req, res) => {
  const logger = getLogger(req);
  const { firstName, lastName, date_of_birth } = req.body;

  // WICHTIG: Immer loggen, auch wenn Logger fehlt
  console.log(`[TOOLS-DEBUG] POST /api/tools/searchPatient aufgerufen`, { firstName, lastName, date_of_birth });
  logger.info({ body: req.body }, `POST /api/tools/searchPatient - Intelligente Patientensuche gestartet`);

  // KRITISCHE VALIDIERUNG: Alle drei Daten müssen vorhanden sein
  if (!firstName || !lastName || !date_of_birth) {
    const missingFields = [];
    if (!firstName) missingFields.push('Vorname');
    if (!lastName) missingFields.push('Nachname');
    if (!date_of_birth) missingFields.push('Geburtsdatum');
    
    logger.error({ missingFields, body: req.body }, 'searchPatient aufgerufen ohne alle erforderlichen Daten');
    console.log(`[TOOLS-DEBUG] FEHLER: searchPatient ohne vollständige Daten aufgerufen. Fehlend: ${missingFields.join(', ')}`);
    
    return res.status(400).json({
      success: false,
      found: false,
      error: 'incomplete_data',
      message: `Bitte geben Sie zuerst alle Daten an: ${missingFields.join(', ')}`,
      missingFields: missingFields
    });
  }

  let client;
  try {
    // Verbindung zur PostgreSQL-Datenbank
    client = await connectDB();
    
    // Intelligente Suche mit Fuzzy-Matching
    const searchResult = await intelligentPatientSearch(firstName, lastName, date_of_birth, client, logger);
    const { bestMatch, highestScore, totalCandidates } = searchResult;

    // Score-basierte Entscheidung (wie in Namen.md dokumentiert)
    if (bestMatch && highestScore >= 85) {
      // Automatischer Treffer bei hohem Score (>= 85)
      logger.info({ score: highestScore }, 'Hoher Confidence Score - automatische Bestätigung');
      
      res.status(200).json({
        success: true,
        patient: {
          patient_id: bestMatch.patient_id,
          firstName: bestMatch.first_name,
          lastName: bestMatch.last_name,
          date_of_birth: bestMatch.date_of_birth,
          phone: bestMatch.phone,
          email: bestMatch.email,
          address: bestMatch.address,
          insurance_type: bestMatch.insurance_type
        },
        found: true,
        confidence: 'high',
        fuzzyScore: Math.round(highestScore),
        totalCandidates,
        message: `Patient automatisch identifiziert (${Math.round(highestScore)}% Übereinstimmung)`
      });
    } 
    else if (bestMatch && highestScore >= 70) {
      // Mittelere Confidence - nachfragen erforderlich (70-84)
      logger.info({ score: highestScore }, 'Mittlerer Confidence Score - Nachfrage erforderlich');
      
      res.status(200).json({
        success: true,
        patient: {
          patient_id: bestMatch.patient_id,
          firstName: bestMatch.first_name,
          lastName: bestMatch.last_name,
          date_of_birth: bestMatch.date_of_birth,
          phone: bestMatch.phone,
          email: bestMatch.email,
          address: bestMatch.address,
          insurance_type: bestMatch.insurance_type
        },
        found: true,
        confidence: 'medium',
        fuzzyScore: Math.round(highestScore),
        totalCandidates,
        requiresConfirmation: true,
        message: `Möglicherweise gefunden: ${bestMatch.first_name} ${bestMatch.last_name}, geboren am ${bestMatch.date_of_birth}. Ist das korrekt?`
      });
    } 
    else {
      // Niedriger Score (<70) - kein zuverlässiger Treffer
      logger.info({ score: highestScore, totalCandidates }, 'Niedriger Confidence Score - kein zuverlässiger Treffer');
      
      res.status(404).json({
        success: false,
        found: false,
        confidence: 'low',
        fuzzyScore: bestMatch ? Math.round(highestScore) : 0,
        totalCandidates,
        message: 'Kein eindeutiger Patient gefunden. Könnten Sie Ihren Namen bitte buchstabieren und ihren Geburtsdatum nochmal nennen das ich keinen fehler mache?',
        suggestion: 'spelling_required'
      });
    }

  } catch (error) {
    logger.error({ error: error.message }, 'Fehler bei der intelligenten Patientensuche');
    res.status(500).json({
      success: false,
      message: 'Datenbankfehler bei der Patientensuche',
      found: false,
      error: error.message
    });
  } finally {
    if (client) {
      await client.end();
    }
  }
});

// 6. COLLECT PATIENT DETAILS
router.post('/collectPatientDetails', async (req, res) => {
  const logger = getLogger(req);
  const { 
    firstName, lastName, date_of_birth, phone, 
    insurance, address, email 
  } = req.body;

  // WICHTIG: Immer loggen, auch wenn Logger fehlt
  console.log(`[TOOLS-DEBUG] POST /api/tools/collectPatientDetails aufgerufen`, { firstName, lastName });
  logger.info({ body: req.body }, `POST /api/tools/collectPatientDetails - Anfrage zur Patientendatenerfassung`);

  // KRITISCHE VALIDIERUNG: Mindestdaten müssen vorhanden sein
  if (!firstName || !lastName || !date_of_birth || !phone) {
    const missingFields = [];
    if (!firstName) missingFields.push('Vorname');
    if (!lastName) missingFields.push('Nachname');
    if (!date_of_birth) missingFields.push('Geburtsdatum');
    if (!phone) missingFields.push('Telefonnummer');
    
    logger.error({ missingFields, body: req.body }, 'collectPatientDetails aufgerufen ohne alle erforderlichen Daten');
    console.log(`[TOOLS-DEBUG] FEHLER: collectPatientDetails ohne vollständige Daten aufgerufen. Fehlend: ${missingFields.join(', ')}`);
    
    return res.status(400).json({
      success: false,
      error: 'incomplete_data',
      message: `Bitte geben Sie zuerst alle erforderlichen Daten an: ${missingFields.join(', ')}`,
      missingFields: missingFields
    });
  }

  let client;
  try {
    // Verbindung zur PostgreSQL-Datenbank
    client = await connectDB();
    
    // Neue Patient-ID generieren (höchste ID + 1)
    const maxIdResult = await client.query('SELECT MAX(patient_id) as max_id FROM patient');
    const newPatientId = (maxIdResult.rows[0].max_id || 0) + 1;

    // Neuen Patienten in die Datenbank einfügen
    const insertQuery = `
      INSERT INTO patient (
        patient_id, first_name, last_name, date_of_birth, 
        phone, email, address, insurance_type, registration_date
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
      RETURNING *
    `;

    const values = [
      newPatientId,
      firstName,
      lastName, 
      date_of_birth,
      phone || '',
      email || '',
      address || '',
      insurance || '',
      new Date().toISOString().split('T')[0] // Datum im YYYY-MM-DD Format
    ];

    logger.info({ insertQuery, values }, 'Inserting new patient');

    const result = await client.query(insertQuery, values);
    const newPatient = result.rows[0];

    res.status(201).json({
      success: true,
      patient: {
        patient_id: newPatient.patient_id,
        firstName: newPatient.first_name,
        lastName: newPatient.last_name,
        date_of_birth: newPatient.date_of_birth,
        phone: newPatient.phone,
        email: newPatient.email,
        address: newPatient.address,
        insurance_type: newPatient.insurance_type,
        registration_date: newPatient.registration_date
      },
      message: 'Patient erfolgreich erstellt'
    });

  } catch (error) {
    logger.error({ error: error.message }, 'Fehler bei der Patientendatenerfassung');
    
    // Prüfe auf Duplikate (falls unique constraints existieren)
    if (error.code === '23505') {
      res.status(409).json({
        success: false,
        message: 'Patient mit diesen Daten existiert bereits',
        error: 'duplicate_patient'
      });
    } else {
      res.status(500).json({
        success: false,
        message: 'Datenbankfehler bei der Patientenerstellung',
        error: error.message
      });
    }
  } finally {
    if (client) {
      await client.end();
    }
  }
});

// WICHTIG: Logger für Tools - garantiert in Log-Datei
function getLogger(req) {
  const logger = req?.app?.locals?.logger;
  if (logger) {
    return logger;
  }
  
  // Fallback: Verwende console (wurde in app.js umgeleitet zu Pino)
  console.log('[TOOLS] Logger Fallback aktiviert - Tool-Aufrufe werden geloggt');
  return console;
}

module.exports = router; 