# Ultravox: Server vs. Client Tools

## Überblick

Ultravox bietet verschiedene Arten von <PERSON> (auch als Function Calling bekannt), um die Fähigkeiten von KI-Agenten zu erweitern, indem sie mit externen Diensten und Systemen verbunden werden. Im Kern sind Tools einfach Funktionen, die Agenten aufrufen können, um bestimmte Aktionen auszuführen oder Informationen abzurufen. <mcreference link="https://docs.ultravox.ai/essentials/tools" index="1">1</mcreference>

Es gibt drei Haupttypen von Tool-Implementierungen in Ultravox:

1. HTTP-Tools (Server-Tools)
2. Client-Tools
3. DataConnection-Tools

## Server-Tools (HTTP-Tools)

Server-Tools werden auf der Serverseite implementiert und sind unabhängig vom verwendeten Kommunikationsmedium des Clients. <mcreference link="https://docs.ultravox.ai/essentials/tools" index="1">1</mcreference>

Charakteristiken von Server-Tools:

- Die Implementierung befindet sich auf dem Websocket-Server
- Können unabhängig vom Kommunikationsmedium des Clients verwendet werden
- Setzen den Antworttyp über den X-Ultravox-Response-Type Header
- Werden für Backend-Operationen verwendet
- Können Authentifizierungsanforderungen und statische Parameterwerte spezifizieren

## Client-Tools

Client-Tools werden im Client-Code aufgerufen und ermöglichen interaktive Elemente in der Anwendung, die durch Benutzerinteraktionen mit dem Agenten gesteuert werden. <mcreference link="https://docs.ultravox.ai/sdk-reference/introduction" index="2">2</mcreference> <mcreference link="https://docs.ultravox.ai/tutorials/clienttools" index="3">3</mcreference>

Charakteristiken von Client-Tools:

- Werden in der Frontend-Anwendung implementiert
- Ermöglichen direkte Interaktion zwischen KI-Agenten und der Frontend-Anwendung
- Setzen den Antworttyp über das responseType-Feld in ihrer Tool-Ergebnismeldung
- Speziell für folgende Zwecke konzipiert:
  - UI-Updates: Ändern von Interface-Elementen in Echtzeit
  - Zustandsverwaltung: Verwalten von Änderungen des Anwendungszustands
  - Benutzerinteraktion: Reagieren auf und Verarbeiten von Benutzeraktionen
  - Event-Handling: Versenden und Verwalten von benutzerdefinierten Events

### Registrierung von Client-Tools

Client-Tools werden mit dem Ultravox Client SDK registriert. Wenn ein Anruf mit einem clientseitig implementierten Tool gestartet wird, wird diese Implementierung aufgerufen, wenn das Modell das Tool aufruft. <mcreference link="https://docs.ultravox.ai/sdk-reference/introduction" index="2">2</mcreference>

```javascript
// Beispiel für die Registrierung eines Client-Tools
registerToolImplementation(name: string, implementation: ClientToolImplementation): void

// Beispiel für eine Tool-Implementierung
const stock_price = (parameters) => {
  // Implementierung
  return `Stock price is ${value}`;
};
```

## Unterschiede zwischen Server- und Client-Tools

Die Hauptunterschiede zwischen Server- und Client-Tools sind: <mcreference link="https://docs.jambonz.org/tutorials/voice-ai/ultravox" index="4">4</mcreference>

1. **Implementierungsort**:
   - Server-Tools: Auf dem Server implementiert
   - Client-Tools: In der Frontend-Anwendung implementiert

2. **Anwendungsfälle**:
   - Server-Tools: Für Backend-Operationen und Dienste, die unabhängig vom Client sein müssen
   - Client-Tools: Für UI-Updates, Zustandsverwaltung und direkte Benutzerinteraktionen

3. **Antworttyp-Einstellung**:
   - Server-Tools: Über den X-Ultravox-Response-Type Header
   - Client-Tools: Über das responseType-Feld in der Tool-Ergebnismeldung

4. **Agent-Reaktion**:
   - Server-Tools: Über den X-Ultravox-Agent-Reaction Header
   - Client-Tools: Über das agent_reaction-Feld in der Tool-Ergebnismeldung

## Beispiele für die Verwendung

### Beispiel für ein Server-Tool

Ein Server-Tool könnte verwendet werden, um Wetterdaten von einer externen API abzurufen oder um einen Anruf an eine bestimmte Telefonnummer weiterzuleiten. <mcreference link="https://docs.jambonz.org/tutorials/voice-ai/ultravox" index="4">4</mcreference>

### Beispiel für ein Client-Tool

Ein Client-Tool könnte verwendet werden, um eine Bestellanzeige in Echtzeit zu aktualisieren, während Kunden über einen KI-Agenten Bestellungen aufgeben. <mcreference link="https://docs.ultravox.ai/tutorials/clienttools" index="3">3</mcreference>

```javascript
// Beispiel für die Definition eines Client-Tools
const selectedTools: SelectedTool[] = [
  {
    "temporaryTool": {
      "modelToolName": "updateOrder",
      "description": "Update order details. Used any time items are added or removed or when the order is finalized.",      
      "dynamicParameters": [
        {
          "name": "orderDetailsData",
          "location": ParameterLocation.BODY,
          "schema": {
            "description": "An array of objects contain order items.",
            "type": "array",
            "items": {
              "type": "object",
              "properties": {
                "name": { "type": "string", "description": "The name of the item to be added to the order." },
                "quantity": { "type": "number", "description": "The quantity of the item for the order." },
                "specialInstructions": { "type": "string", "description": "Any special instructions that pertain to the item." },
                "price": { "type": "number", "description": "The unit price for the item." },
              },
              "required": ["name", "quantity", "price"]
            }
          },
          "required": true
        },
      ],
      "client": {}
    }
  },
];
```

## Fazit

Die Wahl zwischen Server- und Client-Tools hängt von den spezifischen Anforderungen Ihrer Anwendung ab. Server-Tools sind ideal für Backend-Operationen und Dienste, die unabhängig vom Client sein müssen, während Client-Tools perfekt für UI-Updates, Zustandsverwaltung und direkte Benutzerinteraktionen geeignet sind. Ultravox bietet robuste Unterstützung für beide Arten von Tools, was es Entwicklern ermöglicht, die Fähigkeiten ihrer KI-Agenten zu erweitern und interaktive, dynamische Anwendungen zu erstellen.