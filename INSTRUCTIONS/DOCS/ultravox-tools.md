# Ultravox Tools Dokumentation

## Einführung

Tools in Ultravox (auch bekannt als Function Calling) sind eine leistungsstarke Möglichkeit, die Fähigkeiten von KI-Agenten zu erweitern, indem sie mit externen Diensten und Systemen verbunden werden. Im Kern sind Tools einfach Funktionen, die Agenten aufrufen können, um bestimmte Aktionen auszuführen oder Informationen abzurufen. <mcreference link="https://docs.ultravox.ai/essentials/tools" index="1">1</mcreference>

Ultravox bietet sowohl integrierte Tools als auch die Möglichkeit, benutzerdefinierte Tools zu erstellen. <mcreference link="https://docs.ultravox.ai/essentials/tools" index="1">1</mcreference>

## Einsatzmöglichkeiten

Hier sind einige Beispiele, was mit Tools möglich ist:

- <PERSON><PERSON> a<PERSON>, Kinozeiten abrufen, Kalenderereignisse erstellen oder E-Mails senden
- Bestellungen nachschlagen, Informationen zu nicht lieferbaren Artikeln abrufen oder Versandupdates bereitstellen
- Produkt- und Support-Dokumentation für kontextbezogenen Support konsultieren <mcreference link="https://docs.ultravox.ai/gettingstarted/rule4" index="5">5</mcreference>

Jede Funktionalität, die in einer Funktion gekapselt werden kann, kann Ihren Agenten als Tool zur Verfügung gestellt werden. Zusätzlich ruft Ultravox automatisch die zugrundeliegende Funktion auf, sodass Sie sich nicht um die Verbindung der Komponenten kümmern müssen. <mcreference link="https://docs.ultravox.ai/essentials/tools" index="1">1</mcreference>

## Integrierte Tools

Ultravox Realtime enthält derzeit folgende integrierte Tools:

| Tool Name | Tool Aktion |
|-----------|-------------|
| queryCorpus | Ruft relevante Informationen aus einem vorhandenen Corpus (auch bekannt als Wissensdatenbank) ab. |
| hangUp | Beendet den Anruf. Sie können auch Ihre eigenen benutzerdefinierten Tools verwenden, um den Anruf zu beenden. |
| playDtmfSounds | Spielt Dual-Tone Multi-Frequency (auch bekannt als Wähltasten) Töne ab. Siehe DTMF für Details zum Senden und Empfangen von Tönen in Ihren Sprachanwendungen. <mcreference link="https://docs.ultravox.ai/essentials/tools" index="1">1</mcreference> |

Abgesehen davon, dass sie öffentlich sichtbar sind, haben integrierte Tools keine besonderen Eigenschaften. Wenn Sie die Tools auflisten, werden Sie feststellen, dass sie die gleichen Definitionen verwenden, die Sie selbst erstellen können. <mcreference link="https://docs.ultravox.ai/essentials/tools" index="1">1</mcreference>

## Verwendung eines integrierten Tools

Sie geben Ihrem KI-Agenten Zugriff auf Tools, wenn Sie einen Anruf erstellen oder eine neue Anrufphase einleiten.

```json
// Beispiel für einen Request-Body zum Erstellen eines Anrufs mit einem integrierten Tool
{
  "systemPrompt":"Sie sind ein hilfreicher Assistent. Wenn der Anruf natürlich zu Ende geht, verwenden Sie das 'hangUp'-Tool, um den Anruf zu beenden.",
  "selectedTools":[
    { "toolName": "hangUp" }
  ]
};

// Die toolId kann auch verwendet werden
{
  "systemPrompt":"Sie sind ein hilfreicher Assistent. Wenn der Anruf natürlich zu Ende geht, verwenden Sie das 'hangUp'-Tool, um den Anruf zu beenden.",
  "selectedTools":[
    { "toolId": "56294126-5a7d-4948-b67d-3b7e13d55ea7" }
  ]
};
```

**Hinweis:** Die Verwendung integrierter Tools ist die gleiche wie die Verwendung eines anderen benutzerdefinierten dauerhaften Tools, das Sie erstellt haben, mit einem Unterschied: Sie können integrierte Tools überschreiben, indem Sie denselben Namen verwenden. <mcreference link="https://docs.ultravox.ai/essentials/tools" index="1">1</mcreference>

Wenn Sie beispielsweise ein dauerhaftes Tool namens "hangUp" erstellt haben und dieses Tool dann nach Namen (d.h. nicht nach der toolId) bereitstellen, würde Ihr Tool anstelle des integrierten hangUp-Tools verwendet werden. <mcreference link="https://docs.ultravox.ai/essentials/tools" index="1">1</mcreference>

## Anzeigen von Tools

Um alle für Sie verfügbaren Tools anzuzeigen, einschließlich integrierter Tools, verwenden Sie die List Tools API. <mcreference link="https://docs.ultravox.ai/essentials/tools" index="1">1</mcreference>

## Benutzerdefinierte Tools

Benutzerdefinierte Tools ermöglichen die Kommunikation mit der Außenwelt. Alles, was in einer Funktion möglich ist, kann jetzt von Ihrem Agenten über ein benutzerdefiniertes Tool ausgeführt werden. <mcreference link="https://docs.ultravox.ai/essentials/tools" index="1">1</mcreference>

## Tool-Anforderungen

Bei der Definition eines Tools können Sie auch Anforderungen für Agenten oder Anrufe angeben, die das Tool verwenden. Insbesondere können Sie die Authentifizierungsanforderungen und alle Parameter angeben, die mit statischen Werten überschrieben werden müssen. <mcreference link="https://docs.ultravox.ai/essentials/tools" index="1">1</mcreference>

## Tool-Authentifizierung

Ultravox bietet umfassende Unterstützung für die Tool-Authentifizierung. Bei der Erstellung eines Tools müssen Sie angeben, was für eine erfolgreiche Authentifizierung beim Backend-Dienst erforderlich ist. <mcreference link="https://docs.ultravox.ai/essentials/tools" index="1">1</mcreference>

Drei Methoden zum Übergeben von API-Schlüsseln werden unterstützt und werden bei der Erstellung des Tools verwendet. <mcreference link="https://docs.ultravox.ai/essentials/tools" index="1">1</mcreference>

Ihr Tool kann mehrere Optionen zur Erfüllung von Authentifizierungsanforderungen angeben (zum Beispiel, wenn Ihr Server entweder Query- oder Header-Authentifizierung erlaubt). <mcreference link="https://docs.ultravox.ai/essentials/tools" index="1">1</mcreference>

Jede Option kann auch mehrere Anforderungen enthalten, zum Beispiel wenn Ihr Server sowohl eine user_id als auch ein auth_token für diesen Benutzer benötigt. <mcreference link="https://docs.ultravox.ai/essentials/tools" index="1">1</mcreference>

Bei der Definition eines Agenten oder der Erstellung eines Anrufs übergeben Sie den/die Schlüssel in der authTokens-Eigenschaft von selectedTools. <mcreference link="https://docs.ultravox.ai/essentials/tools" index="1">1</mcreference>

Wenn die von Ihnen bereitgestellten Tokens mehrere Optionen erfüllen, wird die erste nicht leere Option verwendet, deren Anforderungen alle erfüllt sind. Eine nicht authentifizierte Option wird, falls vorhanden, nur verwendet, wenn keine andere Option erfüllt werden kann. <mcreference link="https://docs.ultravox.ai/essentials/tools" index="1">1</mcreference>

```json
// Erstellen eines Tools, das einen Query-Parameter namens 'apiKey' verwendet
{
  "name": "stock_price"
  "definition": {
    "description": "Holen Sie den aktuellen Aktienkurs für ein bestimmtes Symbol",
    "requirements": {
      "httpSecurityOptions": {
        "options": [
          "requirements": {
            "myServiceApiKey": {
              "queryApiKey": {
                "name": "apiKey"
              }
            }
          }
        ]
      }
    }
  }
}

// Übergeben des API-Schlüssels während der Anruferstellung
// Anfragen enthalten ?apiKey=your_token_here in der URL
{
  "systemPrompt": ...
  "selectedTools": [
    {
      "toolName": "stock_price"
      "authTokens": {
        "myServiceApiKey": "your_token_here"
      }
    }
  ]
}
```

## Erforderliche Parameterüberschreibungen

Manchmal benötigt Ihr Tool einen Parameter, der bei der Erstellung des Anrufs definiert werden muss, anstatt dass das Modell einen Wert erstellt. In diesen Fällen können Sie verlangen, dass der Parameter bei der Anruferstellung überschrieben wird. <mcreference link="https://docs.ultravox.ai/essentials/tools" index="1">1</mcreference>

Zum Beispiel erfordert das integrierte queryCorpus-Tool, dass die Corpus-ID während der Anruferstellung angegeben wird. <mcreference link="https://docs.ultravox.ai/essentials/tools" index="1">1</mcreference>

## Über Ultravox

Ultravox ist ein Open-Weight Speech Language Model (SLM), das darauf trainiert wurde, Sprache auf natürliche Weise zu verstehen, genau wie Menschen. <mcreference link="https://www.ultravox.ai/" index="3">3</mcreference>

Im Gegensatz zu anderen sprachbasierten Systemen integriert Ultravox die Spracherkennung direkt, ohne sich auf die Umwandlung von Sprache in Text zu verlassen. Dies macht Ultravox schneller, zuverlässiger und natürlicher. <mcreference link="https://www.ultravox.ai/" index="3">3</mcreference>

Ultravox ist die Enterprise Voice AI-Plattform, die von Grund auf für Skalierbarkeit konzipiert wurde. <mcreference link="https://www.ultravox.ai/" index="3">3</mcreference>

## Telefonie-Integration

Ultravox bietet umfassende Unterstützung für DTMF (Dual-Tone Multi-Frequency) Töne und ermöglicht sowohl das Senden als auch das Empfangen von Tönen während Telefonanrufen. Dies ermöglicht KI-Agenten, mit traditionellen Telefonsystemen zu interagieren und ermöglicht den Aufbau von Sprachanwendungen, die auf Tastatureingaben reagieren können. <mcreference link="https://docs.ultravox.ai/essentials/telephony" index="4">4</mcreference>

## Die fünf Regeln von Ultravox

Die vierte Regel für den Aufbau mit Ultravox lautet: "Tools sind nur Funktionen". <mcreference link="https://docs.ultravox.ai/gettingstarted/rule4" index="5">5</mcreference>

Vielseitig? Check. Leistungsstark? Check. Einfach zu implementieren? Check. <mcreference link="https://docs.ultravox.ai/gettingstarted/rule4" index="5">5</mcreference>