# RAG Implementation - queryCorpus Tool

## Übersicht
Das `queryCorpus` Tool wurde implementiert, um die Weiterleitungsregeln und Terminkriterien aus dem System-Prompt in eine RAG-Wissensdatenbank zu verlagern. Dies macht den Prompt sauberer und flexibler.

## Tool-Definition
```javascript
{
  temporaryTool: {
    modelToolName: 'queryCorpus',
    description: 'Durchsucht die Wissensdatenbank nach Weiterleitungsregeln und Terminkriterien',
    dynamicParameters: [
      {
        name: 'query',
        location: 'PARAMETER_LOCATION_BODY',
        schema: {
          type: 'string',
          description: 'Suchanfrage für die Wissensdatenbank (z.B. "Weiterleitung Notfall", "Terminkategorie allgemeine Kontrolle")'
        },
        required: true
      }
    ],
    parameterOverrides: {
      corpus_id: process.env.ULTRAVOX_CORPUS_ID || 'default_corpus_id',
      max_results: 5
    }
  }
}
```

## Umgebungsvariable
```bash
ULTRAVOX_CORPUS_ID=ihre_corpus_id_hier
```

## Verwendung im System-Prompt
```
3. Entscheidung treffen und Tool-Nutzung: 
  - Nutze das Tool 'queryCorpus' um zu prüfen, ob das Anliegen eine Weiterleitung erfordert oder ein Terminwunsch ist
  - Beispiel-Queries: "Weiterleitung bei Notfall", "Terminkategorie allgemeine Kontrolle", "Weiterleitung Operation"
```

## Was in das RAG-Corpus gehört:

### Weiterleitungsregeln
- Medizinische Notfälle (starke Schmerzen, Sehverlust, etc.)
- Operationstermine und -details
- Sehschule für Kinder unter 6 Jahren
- Komplexe medizinische Fragen
- Technische Probleme
- Patientendokumente/Befunde

### Terminkriterien
- allgemeineKontrolle: Routineuntersuchungen, allgemeine Augenuntersuchungen
- entzuendungenBeratung: Behandlung von Entzündungen
- kinder: Termine für Kinder (ab 6 Jahren)
- lidBeratung: Beratung zu Augenlid-Themen
- botoxBeratung: Botox-Behandlungen im Augenbereich
- beratungRefraktiveChirurgie: Augenlasern, Lasik, Brillenfreiheit
- laserSprechstunde: Laserbehandlungen oder deren Nachkontrollen

## Vorteile:
1. **Sauberer System-Prompt**: Weniger Text, fokussiert auf den Ablauf
2. **Flexibilität**: Kriterien können ohne Code-Änderung angepasst werden
3. **Konsistenz**: Einheitliche Entscheidungsgrundlage
4. **Erweiterbarkeit**: Neue Kategorien einfach hinzufügbar

## Nächste Schritte:
1. ULTRAVOX_CORPUS_ID in der .env-Datei setzen
2. Weiterleitungsregeln und Terminkriterien in Ultravox-Corpus eintragen
3. Service neu starten
4. Testen mit Anrufen 