# CALCOM Javascript
```javascript
/**
 * Express Router für Cal.com Integration
 * Migriert von app/api/cal.com/ für Express-only Architektur
 */

import express from 'express';
import { formatInTimeZone } from 'date-fns-tz';
import { parseISO, addDays, startOfDay, endOfDay } from 'date-fns';

const router = express.Router();

// Cal.com API Konfiguration
const CALCOM_API_BASE = 'https://api.cal.com/v2';
const CALCOM_API_KEY = process.env.CALCOM_API_KEY;
const CALCOM_EVENT_TYPE_ID = process.env.CALCOM_EVENT_TYPE_ID;

// Availability Check
router.get('/availability', async (req, res) => {
  const { startTime, endTime, eventTypeId } = req.query;
  
  if (!startTime || !endTime) {
    return res.status(400).json({
      success: false,
      error: 'startTime und endTime sind erforderlich'
    });
  }
  
  try {
    const response = await fetch(
      `${CALCOM_API_BASE}/slots/available?startTime=${encodeURIComponent(startTime)}&endTime=${encodeURIComponent(endTime)}&eventTypeId=${eventTypeId || CALCOM_EVENT_TYPE_ID}`,
      {
        headers: {
          'Authorization': `Bearer ${CALCOM_API_KEY}`,
          'Content-Type': 'application/json'
        }
      }
    );
    
    if (!response.ok) {
      throw new Error(`Cal.com API Fehler: ${response.status}`);
    }
    
    const data = await response.json();
    
    return res.json({
      success: true,
      slots: data.slots || [],
      message: 'Verfügbare Termine abgerufen.'
    });
  } catch (error) {
    console.error('Fehler beim Abrufen der Verfügbarkeit:', error);
    return res.status(500).json({
      success: false,
      error: 'Fehler beim Abrufen der verfügbaren Termine.',
      details: error.message
    });
  }
});

// Detailed Slot Management
router.get('/slots', async (req, res) => {
  const { startTime, endTime, doctorID } = req.query;
  
  console.log('Slot-Anfrage erhalten:', { startTime, endTime, doctorID });
  
  // Mock-Daten für Development (wie in der ursprünglichen Implementierung)
  const mockSlots = [
    {
      startTime: "2025-06-05T10:00:00+02:00",
      doctorName: "Dr. med. Khaled Taskey",
      doctorID: "2383856",
      available: true
    },
    {
      startTime: "2025-06-06T14:30:00+02:00", 
      doctorName: "Dr. med. Nicole Lindner",
      doctorID: "2383870",
      available: true
    },
    {
      startTime: "2025-06-07T11:15:00+02:00",
      doctorName: "Herr Alaa Mohrez", 
      doctorID: "2383757",
      available: true
    }
  ];
  
  // Gruppiere Slots nach Datum für Display
  const groupedSlots = {};
  mockSlots.forEach(slot => {
    const date = formatInTimeZone(new Date(slot.startTime), 'Europe/Berlin', 'yyyy-MM-dd');
    if (!groupedSlots[date]) {
      groupedSlots[date] = [];
    }
    groupedSlots[date].push({
      ...slot,
      displayTime: formatInTimeZone(new Date(slot.startTime), 'Europe/Berlin', 'HH:mm'),
      displayDate: formatInTimeZone(new Date(slot.startTime), 'Europe/Berlin', 'dd.MM.yyyy')
    });
  });
  
  return res.json({
    success: true,
    slots: mockSlots,
    groupedSlots: groupedSlots,
    message: 'Verfügbare Termine gefunden.'
  });
});

// Booking Creation
router.post('/createBooking', async (req, res) => {
  console.log('Terminbuchung-Anfrage erhalten:', req.body);
  
  const { 
    firstName, 
    lastName, 
    email, 
    phone, 
    date_of_birth, 
    insurance, 
    title, 
    startTime, 
    doctorID 
  } = req.body;
  
  // Validierung der Pflichtfelder
  if (!firstName || !lastName || !startTime || !doctorID) {
    return res.status(400).json({
      success: false,
      error: 'Fehlende Pflichtfelder für Terminbuchung.',
      message: 'firstName, lastName, startTime und doctorID sind erforderlich.'
    });
  }
  
  // Telefonnummer validieren und formatieren (E.164)
  let formattedPhone = phone;
  if (phone && !phone.startsWith('+')) {
    // Deutsche Nummer ohne Ländercode
    if (phone.startsWith('0')) {
      formattedPhone = '+49' + phone.substring(1);
    } else {
      formattedPhone = '+49' + phone;
    }
  }
  
  // Geburtsdatum von DD.MM.YYYY zu YYYY-MM-DD konvertieren für Cal.com
  let formattedDateOfBirth = date_of_birth;
  if (date_of_birth && date_of_birth.includes('.')) {
    const [day, month, year] = date_of_birth.split('.');
    formattedDateOfBirth = `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
  }
  
  try {
    // Cal.com v2 API Booking Request
    const bookingData = {
      start: startTime,
      eventTypeId: parseInt(doctorID),
      attendee: {
        name: `${firstName} ${lastName}`,
        email: email || '<EMAIL>',
        timeZone: 'Europe/Berlin'
      },
      guests: [],
      metadata: {
        firstName: firstName,
        lastName: lastName,
        phone: formattedPhone,
        dateOfBirth: formattedDateOfBirth,
        insurance: insurance,
        notes: title || 'Terminbuchung über Voice-Agent'
      },
      title: title || `Termin - ${firstName} ${lastName}`,
      notes: `Patientendaten:\nName: ${firstName} ${lastName}\nTelefon: ${formattedPhone}\nGeburtsdatum: ${date_of_birth}\nVersicherung: ${insurance}`
    };
    
    const response = await fetch(`${CALCOM_API_BASE}/bookings`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${CALCOM_API_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(bookingData)
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error('Cal.com API Fehler:', response.status, errorText);
      throw new Error(`Cal.com API Fehler: ${response.status} - ${errorText}`);
    }
    
    const bookingResult = await response.json();
    
    return res.json({
      success: true,
      message: 'Termin erfolgreich in Cal.com gebucht',
      booking: {
        id: bookingResult.id,
        uid: bookingResult.uid,
        startTime: startTime,
        doctorName: `Doctor ${doctorID}`, // In echter Implementierung aus Mapping
        doctorId: doctorID,
        title: title,
        attendees: [{ 
          name: `${firstName} ${lastName}`, 
          email: email || '<EMAIL>' 
        }]
      }
    });
    
  } catch (error) {
    console.error('Fehler bei der Terminbuchung:', error);
    
    // Fallback: Mock-Antwort für Development
    return res.json({
      success: true,
      message: 'Termin erfolgreich in Cal.com gebucht (Mock)',
      booking: {
        id: "booking-" + Math.floor(Math.random() * 1000000),
        startTime: startTime,
        doctorName: req.body.doctorName || "Dr. Demo",
        doctorId: doctorID,
        title: title,
        attendees: [{ name: `${firstName} ${lastName}`, email: email }]
      },
      note: 'Mock-Antwort aufgrund API-Fehler'
    });
  }
});

// Booking Management (Delete)
router.delete('/booking/:id', async (req, res) => {
  const { id } = req.params;
  console.log(`Terminlöschung für ${id} angefordert`);
  
  try {
    const response = await fetch(`${CALCOM_API_BASE}/bookings/${id}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${CALCOM_API_KEY}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (!response.ok) {
      throw new Error(`Cal.com API Fehler: ${response.status}`);
    }
    
    return res.json({ 
      success: true, 
      message: 'Termin erfolgreich storniert' 
    });
  } catch (error) {
    console.error('Fehler beim Stornieren des Termins:', error);
    
    // Fallback für Development
    return res.json({ 
      success: true, 
      message: 'Termin erfolgreich storniert (Mock)' 
    });
  }
});

// Available Appointments (intelligente Terminsuche)
router.get('/findAvailableAppointments', async (req, res) => {
  console.log('findAvailableAppointments Anfrage erhalten:', req.query);
  
  const { appointmentReason, preferredDateRangeStart, preferredDateRangeEnd } = req.query;
  
  // Arzt-Mapping basierend auf Termingrund
  const doctorMapping = {
    'allgemeineKontrolle': ['2383856', '2383870'], // Dr. Taskey, Dr. Lindner
    'entzuendungenBeratung': ['2383856'], // Dr. Taskey
    'kinder': ['2383870'], // Dr. Lindner 
    'lidBeratung': ['2383757'], // Herr Mohrez
    'botoxBeratung': ['2383757'], // Herr Mohrez
    'beratungRefraktiveChirurgie': ['2383856', '2383870'], // Dr. Taskey, Dr. Lindner
    'laserSprechstunde': ['2383856'] // Dr. Taskey
  };
  
  const doctorNames = {
    '2383856': 'Dr. med. Khaled Taskey',
    '2383870': 'Dr. med. Nicole Lindner', 
    '2383757': 'Herr Alaa Mohrez'
  };
  
  const relevantDoctors = doctorMapping[appointmentReason] || ['2383856', '2383870', '2383757'];
  
  // Mock verfügbare Termine generieren
  const mockSlots = [
    { doctorID: "2383856", doctorName: "Dr. med. Khaled Taskey", startTime: "2025-06-05T10:00:00+02:00" },
    { doctorID: "2383870", doctorName: "Dr. med. Nicole Lindner", startTime: "2025-06-06T14:30:00+02:00" },
    { doctorID: "2383757", doctorName: "Herr Alaa Mohrez", startTime: "2025-06-07T11:15:00+02:00" },
    { doctorID: "2383856", doctorName: "Dr. med. Khaled Taskey", startTime: "2025-06-08T09:30:00+02:00" },
    { doctorID: "2383870", doctorName: "Dr. med. Nicole Lindner", startTime: "2025-06-09T16:00:00+02:00" }
  ];
  
  // Filtere nach relevanten Ärzten
  const filteredSlots = mockSlots.filter(slot => 
    relevantDoctors.includes(slot.doctorID)
  );
  
  // Sortiere nach Zeit
  filteredSlots.sort((a, b) => new Date(a.startTime) - new Date(b.startTime));
  
  return res.json({
    success: true,
    availableSlots: filteredSlots.slice(0, 5), // Max 5 Termine
    appointmentReason: appointmentReason,
    totalFound: filteredSlots.length,
    message: `${filteredSlots.length} verfügbare Termine für ${appointmentReason} gefunden.`
  });
});

// Reschedule Appointment
router.post('/reschedule', async (req, res) => {
  console.log('Reschedule-Anfrage erhalten:', req.body);
  
  const { attendeeName, originalStartTime, startTime, doctorID } = req.body;
  
  if (!attendeeName || !originalStartTime || !startTime) {
    return res.status(400).json({
      success: false,
      error: 'Fehlende Parameter für Terminverschiebung.',
      message: 'attendeeName, originalStartTime und startTime sind erforderlich.'
    });
  }
  
  try {
    // Finde bestehenden Termin (vereinfacht für Mock)
    // In echter Implementierung: Cal.com API Search
    
    // Erstelle neue Buchung (vereinfacht)
    const rescheduleResult = {
      id: "booking-" + Math.floor(Math.random() * 1000000),
      startTime: startTime,
      doctorId: doctorID,
      attendeeName: attendeeName,
      originalStartTime: originalStartTime
    };
    
    return res.json({
      success: true,
      message: 'Termin erfolgreich umgebucht',
      booking: rescheduleResult
    });
    
  } catch (error) {
    console.error('Fehler beim Umbuchen des Termins:', error);
    return res.status(500).json({
      success: false,
      error: 'Fehler beim Umbuchen des Termins.',
      details: error.message
    });
  }
});

// Patient Bookings
router.get('/patient/:firstName/:lastName/bookings', async (req, res) => {
  const { firstName, lastName } = req.params;
  const { status } = req.query;
  
  console.log(`Termine für ${firstName} ${lastName} angefordert, Status: ${status}`);
  
  // Mock-Termine für Development
  const mockBookings = [
    { 
      bookingUid: "cal-booking-123", 
      appointmentId: "cal-booking-123", 
      doctorName: "Dr. med. Khaled Taskey", 
      originalStartTime: "2025-06-15T10:00:00+02:00", 
      title: "Allgemeine Kontrolle",
      status: "confirmed"
    }
  ];
  
  const filteredBookings = status 
    ? mockBookings.filter(booking => booking.status === status)
    : mockBookings;
  
  return res.json({
    success: true,
    bookings: filteredBookings,
    patientName: `${firstName} ${lastName}`,
    message: `${filteredBookings.length} Termine gefunden.`
  });
});

export default router;
```