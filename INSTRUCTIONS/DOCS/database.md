# DATABASE Javascript

```javascript
/**
 * Express Router für Patientendaten-Management
 * <PERSON><PERSON><PERSON><PERSON> von app/api/patientdata/ für Express-only Architektur
 */

import express from 'express';
import path from 'path';
import { validateParams } from '../../core/ai-utils.js';

const router = express.Router();

// Patient Search
router.get('/search', async (req, res) => {
  const { firstName, lastName, date_of_birth, query } = req.query;
  console.log('GET Patientensuche nach:', { firstName, lastName, date_of_birth, query });
  
  // Erweiterte Suche mit query Parameter (wie in search/route.js)
  if (query) {
    try {
      // Versuche PostgreSQL/Supabase zuerst
      if (process.env.DB_TYPE === 'postgres' && process.env.SUPABASE_URL) {
        const { createClient } = await import('@supabase/supabase-js');
        const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_ANON_KEY);
        
        // Überprüfe ob query ein Datum ist (DD.MM.YYYY Format)
        const dateMatch = query.match(/^(\d{1,2})\.(\d{1,2})\.(\d{4})$/);
        
        let supabaseQuery = supabase.from('patients').select('*');
        
        if (dateMatch) {
          supabaseQuery = supabaseQuery.eq('date_of_birth', query);
        } else {
          supabaseQuery = supabaseQuery.or(`first_name.ilike.%${query}%,last_name.ilike.%${query}%`);
        }
        
        const { data: patients, error } = await supabaseQuery;
        
        if (error) throw error;
        
        return res.json({
          success: true,
          patients: patients || [],
          found: patients?.length > 0,
          message: patients?.length > 0 ? 'Patienten gefunden.' : 'Keine Patienten gefunden.',
          source: 'supabase'
        });
      }
    } catch (supabaseError) {
      console.log('Supabase Fehler, verwende SQLite Fallback:', supabaseError.message);
    }
    
    // SQLite Fallback
    try {
      const sqlite3Module = await import('sqlite3');
      const { open } = await import('sqlite');
      
      const dbPath = path.resolve(process.cwd(), 'Data', 'patients.db');
      const db = await open({
        filename: dbPath,
        driver: sqlite3Module.default.Database
      });
      
      // Überprüfe ob query ein Datum ist
      const dateMatch = query.match(/^(\d{1,2})\.(\d{1,2})\.(\d{4})$/);
      
      let sql, params;
      
      if (dateMatch) {
        sql = 'SELECT * FROM patients WHERE date_of_birth = ?';
        params = [query];
      } else {
        sql = 'SELECT * FROM patients WHERE lower(first_name) LIKE lower(?) OR lower(last_name) LIKE lower(?)';
        params = [`%${query}%`, `%${query}%`];
      }
      
      const patients = await db.all(sql, ...params);
      await db.close();
      
      return res.json({
        success: true,
        patients: patients || [],
        found: patients?.length > 0,
        message: patients?.length > 0 ? 'Patienten gefunden.' : 'Keine Patienten gefunden.',
        source: 'sqlite'
      });
      
    } catch (sqliteError) {
      console.error('SQLite Fehler:', sqliteError);
      return res.status(500).json({
        success: false,
        error: 'Datenbankfehler bei der Patientensuche.',
        details: sqliteError.message
      });
    }
  }
  
  // Klassische Suche mit separaten Parametern
  if (!firstName && !lastName && !date_of_birth) {
    return res.status(400).json({ 
      success: false, 
      error: 'Suchkriterien fehlen.',
      found: false,
      message: 'Bitte geben Sie Vorname, Nachname oder Geburtsdatum an.'
    });
  }

  // Mock Patient für Tests (wie in server.js)
  if (String(lastName).toLowerCase().includes('müller')) {
    const muellerPatient = {
      patient_id: 12345,
      first_name: "Thomas",
      last_name: "Müller",
      date_of_birth: "15.05.1978",
      phone: "+***********",
      email: "<EMAIL>",
      address: "Musterstraße 123, 12345 Berlin",
      insurance_type: "gesetzlich",
      registration_date: "01.01.2022",
      last_visit: "10.03.2025",
      doctor: "Dr. med. Khaled Taskey"
    };
    
    return res.json({
      success: true,
      patients: [muellerPatient],
      found: true,
      message: 'Patient eindeutig identifiziert.',
      patient: {
        firstName: muellerPatient.first_name,
        lastName: muellerPatient.last_name,
        dateOfBirth: muellerPatient.date_of_birth,
        phone: muellerPatient.phone,
        email: muellerPatient.email || ""
      }
    });
  }

  try {
    const sqlite3Module = await import('sqlite3');
    const { open } = await import('sqlite');
    
    const dbPath = path.resolve(process.cwd(), 'Data', 'patients.db');
    const db = await open({
      filename: dbPath,
      driver: sqlite3Module.default.Database
    });
    
    let sql = 'SELECT * FROM patients WHERE 1=1';
    const params = [];
    
    if (firstName) {
      sql += ' AND lower(first_name) LIKE lower(?)';
      params.push(`%${firstName}%`);
    }
    if (lastName) {
      sql += ' AND lower(last_name) LIKE lower(?)';
      params.push(`%${lastName}%`);
    }
    if (date_of_birth) {
      sql += ' AND date_of_birth = ?';
      params.push(date_of_birth);
    }
    
    const patients = await db.all(sql, ...params);
    await db.close();
    
    if (patients.length === 0) {
      return res.json({ 
        success: true, 
        patients: [],
        found: false,
        message: 'Kein Patient mit diesen Kriterien gefunden.'
      });
    } else if (patients.length === 1) {
      const foundPatient = patients[0];
      return res.json({ 
        success: true, 
        patients: patients,
        found: true,
        message: 'Patient eindeutig identifiziert.',
        patient: {
          firstName: foundPatient.first_name,
          lastName: foundPatient.last_name,
          dateOfBirth: foundPatient.date_of_birth,
          phone: foundPatient.phone,
          email: foundPatient.email || ""
        }
      });
    } else {
      return res.json({ 
        success: true, 
        patients: patients,
        found: true,
        message: `${patients.length} Patienten gefunden. Bitte weitere Kriterien zur eindeutigen Identifikation angeben.`
      });
    }
  } catch (error) {
    console.error('Fehler bei der Datenbankabfrage:', error);
    return res.status(500).json({ 
      success: false, 
      error: 'Interner Serverfehler bei der Patientensuche.', 
      details: error.message,
      found: false,
      message: 'Es ist ein Fehler bei der Suche aufgetreten. Bitte versuchen Sie es erneut.'
    });
  }
});

// Patient Details by ID
router.get('/:id', async (req, res) => {
  const { id } = req.params;
  
  try {
    const sqlite3Module = await import('sqlite3');
    const { open } = await import('sqlite');
    
    const dbPath = path.resolve(process.cwd(), 'Data', 'patients.db');
    const db = await open({
      filename: dbPath,
      driver: sqlite3Module.default.Database
    });
    
    const patient = await db.get('SELECT * FROM patients WHERE patient_id = ?', id);
    await db.close();
    
    if (!patient) {
      return res.status(404).json({
        success: false,
        error: 'Patient nicht gefunden.'
      });
    }
    
    return res.json({
      success: true,
      patient: patient
    });
  } catch (error) {
    console.error('Fehler beim Abrufen des Patienten:', error);
    return res.status(500).json({
      success: false,
      error: 'Interner Serverfehler.'
    });
  }
});

// Update Patient
router.put('/:id', async (req, res) => {
  const { id } = req.params;
  const updateData = req.body;
  
  try {
    const sqlite3Module = await import('sqlite3');
    const { open } = await import('sqlite');
    
    const dbPath = path.resolve(process.cwd(), 'Data', 'patients.db');
    const db = await open({
      filename: dbPath,
      driver: sqlite3Module.default.Database
    });
    
    const fields = [];
    const values = [];
    
    Object.keys(updateData).forEach(key => {
      if (updateData[key] !== undefined) {
        fields.push(`${key} = ?`);
        values.push(updateData[key]);
      }
    });
    
    values.push(id);
    
    const sql = `UPDATE patients SET ${fields.join(', ')} WHERE patient_id = ?`;
    await db.run(sql, ...values);
    
    const updatedPatient = await db.get('SELECT * FROM patients WHERE patient_id = ?', id);
    await db.close();
    
    return res.json({
      success: true,
      message: 'Patient erfolgreich aktualisiert.',
      patient: updatedPatient
    });
  } catch (error) {
    console.error('Fehler beim Aktualisieren des Patienten:', error);
    return res.status(500).json({
      success: false,
      error: 'Interner Serverfehler beim Aktualisieren.'
    });
  }
});

// Update Last Visit
router.put('/:id/lastVisit', async (req, res) => {
  const { id } = req.params;
  const { lastVisit, doctor } = req.body;
  
  try {
    const sqlite3Module = await import('sqlite3');
    const { open } = await import('sqlite');
    
    const dbPath = path.resolve(process.cwd(), 'Data', 'patients.db');
    const db = await open({
      filename: dbPath,
      driver: sqlite3Module.default.Database
    });
    
    await db.run(
      'UPDATE patients SET last_visit = ?, doctor = ? WHERE patient_id = ?',
      [lastVisit, doctor, id]
    );
    
    const updatedPatient = await db.get('SELECT * FROM patients WHERE patient_id = ?', id);
    await db.close();
    
    return res.json({
      success: true,
      message: 'Letzter Besuch erfolgreich aktualisiert.',
      patient: updatedPatient
    });
  } catch (error) {
    console.error('Fehler beim Aktualisieren des letzten Besuchs:', error);
    return res.status(500).json({
      success: false,
      error: 'Interner Serverfehler beim Aktualisieren des letzten Besuchs.'
    });
  }
});

// Patient Registration
router.post('/collectDetails', async (req, res) => {
  console.log('Neue Patientendaten erhalten:', req.body);
  const { firstName, lastName, date_of_birth, phone, email, address, insurance } = req.body;

  if (!firstName || !lastName || !date_of_birth || !phone) {
      return res.status(400).json({
          success: false,
          error: "Fehlende Pflichtfelder für Patientenerfassung.",
          message: "Vorname, Nachname, Geburtsdatum und Telefonnummer sind erforderlich."
      });
  }
  
  try {
    const sqlite3Module = await import('sqlite3');
    const { open } = await import('sqlite');
    
    const dbPath = path.resolve(process.cwd(), 'Data', 'patients.db');
    const db = await open({
      filename: dbPath,
      driver: sqlite3Module.default.Database
    });
    
    const now = new Date().toISOString().split('T')[0];
    
    const result = await db.run(
      `INSERT INTO patients 
      (first_name, last_name, date_of_birth, phone, email, address, insurance_type, registration_date) 
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        firstName, 
        lastName, 
        date_of_birth, 
        phone, 
        email || '', 
        address || '', 
        insurance || 'Unbekannt',
        now
      ]
    );
    
    const newPatient = await db.get('SELECT * FROM patients WHERE patient_id = ?', result.lastID);
    await db.close();
    
    return res.status(201).json({
      success: true,
      message: "Patient erfolgreich angelegt.",
      patient: {
          patient_id: newPatient.patient_id,
          firstName: newPatient.first_name,
          lastName: newPatient.last_name,
          dateOfBirth: newPatient.date_of_birth,
          phone: newPatient.phone,
          email: newPatient.email,
      }
    });
  } catch (error) {
    console.error('Fehler beim Speichern des Patienten:', error);
    return res.status(500).json({
        success: false,
        error: "Interner Serverfehler beim Speichern des Patienten.",
        details: error.message
    });
  }
});

// Booking Search (vereinfachte Suche für Terminbuchungen)
router.get('/search-for-booking', async (req, res) => {
  const { dob, lastName } = req.query;
  
  if (!dob || !lastName) {
    return res.status(400).json({
      success: false,
      error: 'Geburtsdatum und Nachname sind erforderlich.',
      message: 'Bitte geben Sie Geburtsdatum (DD.MM.YYYY) und Nachname an.'
    });
  }
  
  try {
    const sqlite3Module = await import('sqlite3');
    const { open } = await import('sqlite');
    
    const dbPath = path.resolve(process.cwd(), 'Data', 'patients.db');
    const db = await open({
      filename: dbPath,
      driver: sqlite3Module.default.Database
    });
    
    const patient = await db.get(
      'SELECT * FROM patients WHERE date_of_birth = ? AND lower(last_name) = lower(?)',
      [dob, lastName]
    );
    await db.close();
    
    if (!patient) {
      return res.json({
        success: true,
        found: false,
        message: 'Kein Patient mit diesen Daten gefunden.'
      });
    }
    
    return res.json({
      success: true,
      found: true,
      message: 'Patient gefunden.',
      patient: {
        patient_id: patient.patient_id,
        firstName: patient.first_name,
        lastName: patient.last_name,
        dateOfBirth: patient.date_of_birth,
        phone: patient.phone,
        email: patient.email || ""
      }
    });
  } catch (error) {
    console.error('Fehler bei der Booking-Suche:', error);
    return res.status(500).json({
      success: false,
      error: 'Interner Serverfehler bei der Suche.',
      details: error.message
    });
  }
});

export default router;
```
