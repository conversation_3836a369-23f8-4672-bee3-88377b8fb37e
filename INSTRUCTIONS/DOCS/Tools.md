# **TOOLS UND VARIABLEN:**

1. **TRANSFER CALL**: <PERSON><PERSON> den Anruf an einen Menschen weiter
	- NAME: 'transferCall' 
	- VARIABLEN: 'transferReason'

2. **SWITCH CALL STAGE:**  Übergibt den Anrufer an einer der Agent 2
	- NAME: 'switchCallStage'
	- VARIABLEN: 'terminGrund'

3. **GET AVAILABLE SLOTS:** Ruft verfügbare Termine ab, wenn der Anrufer keinen Termin vorschlagen kann dann schlage dem Anrufer drei zufällige Termine innerhalb der nächsten 14 Tage zurück
	- NAME: 'getAvailableAppointments'
	- VARIABLEN: 'appointmentReason', 'startTime', 'endTime'

4. **CREATE BOOKING:** Bucht einen Termin für den Patienten bei Cal.com, nachdem ein spezifischer Zeitslot und Arzt durch 'getAvailableAppointments' identifiziert und vom Patienten bestätigt wurde.
	- NAME: 'createBooking'
	- VARIABLEN: 'firstName', 'lastName', 'date_of_birth', 'phone', 'insurance', 'address', 'email', 'startTime', 'title', 'doctorID'

5. **SEARCH PATIENT:** Sucht nach Patienten in der Datenbank anhand von Vorname, Nachname und Geburtsdatum (Format: DD.MM.YYYY).
	- NAME: 'searchPatient'
	- VARIABLEN: 'firstName', 'lastName', 'date_of_birth'

6. **COLLECT PATIENT DETAILS:** Erfasst und speichert die Daten eines neuen Patienten in der lokalen Datenbank. Liefert bei Erfolg Patientendaten.
	- NAME: 'collectPatientDetails'
	- VARIABLEN: 'firstName', 'lastName', 'date_of_birth', 'phone', 'insurance', 'address', 'email',