slots
Get all bookable slots between a datetime range
GET/slots
https://api.cal.com/v1/slots

# Authorizations
apiKey: string query required

# Query Parameters
apiKey: string required
Your API Key
Example:
"cal_live_<unique_identifier>"

**eventTypeId:** number
The event type Id to fetch available slots against


**startTime:** string required
Start time of the slot lookup

**endTime:** string required
End time of the slot lookup


**timeZone:** string
TimeZone for the slot lookup


**usernameList:** string[]
An array of usernames, or team slug in case of a team event [To be used when not using eventTypeId]


**eventTypeSlug:** string
Slug of the event type to fetch available slots against [To be used when not using eventTypeId]


**orgSlug:** string
Slug of the organization that the user belongs to, in case the user belongs to an organization [To be used when not using eventTypeId]

**isTeamEvent:** boolean
True if the event is a team event

# Example

request:

```javascript
const options = {method: 'GET'};

fetch('https://api.cal.com/v1/slots', options)
  .then(response => response.json())
  .then(response => console.log(response))
  .catch(err => console.error(err));
```

response: 200

```json
{
  "slots": {
    "2024-04-13": [
      {
        "time": "2024-04-13T11:00:00+04:00"
      },
      {
        "time": "2024-04-13T12:00:00+04:00"
      },
      {
        "time": "2024-04-13T13:00:00+04:00"
      }
    ]
  }
}
```