---
title: SIP Decline
subtitle: Reject an incoming call with a specific status
---

The session ends immediately after the action is executed.

```json
{
  "verb": "sip:decline",
  "status": 480,
  "reason": "Gone Fishing",
  "headers" : {
    "Retry-After": 1800
  }
}
```

## Parameters

<ParamField path="status" type="number" required={true}>
  A valid SIP status code in the range `4XX - 6XX`.
</ParamField>

<ParamField path="headers" type="object" required={false}>
  SIP headers to include in the response.
</ParamField>

<ParamField path="reason" type="string" required={false}>
  A brief description.  
  Default: The well-known SIP reasons associated with the specified status code.
</ParamField>


---
title: SIP Refer
subtitle: Perform a blind transfer of a call by sending a SIP REFER
---

```json
{
  "verb": "sip:refer",
  "referTo": +15083084809,
  "actionHook": "/action"
}
```

## Parameters

<ParamField path="referTo" type="string" required={true}>
  A SIP URI or a phone number/user identifier.
</ParamField>

<ParamField path="actionHook" type="string" required={false}>
  A webhook to call when the transfer has completed.
</ParamField>

<ParamField path="eventHook" type="string" required={false}>
  A webhook to call when NOTIFY messages of follow-on call status are received.
</ParamField>

<ParamField path="headers" type="object" required={false}>
  Additional SIP headers to include in the response.
</ParamField>

<ParamField path="referredBy" type="string" required={false}>
  A SIP URI or a phone number/user identifier.  
  If not provided, it will default to the identity of the party being transferred.
</ParamField>

The sip:refer verb completes when one of these conditions are met:
- a failure response is received to the REFER
- a 202 Accepted is received in response to the REFER, and a NOTIFY of the follow-on call status with a final call status is received.

The sip:refer has an action hook that will provide details of the final result, 
as well as an event hook that is called for every NOTIFY received after a successful REFER.

### actionHook properties

The **actionHook** webhook will contain the following additional parameters:

- `referStatus`: the sip status response to the REFER request
- `final_referred_call_status` - the final sip status of the subsequent call to the transferee.  This is only provided in the case where the REFER is accepted and NOTIFY requests are received from the far end.

### eventHook properties

The **eventHook** webhook will contain two parameters: `event` and `call_status`.  The `event` parameter will always be `transfer-status` and the `call-status` will contain a sip status received in a NOTIFY after a successful REFER; e.g.
```json
{
	"event": "transfer-status",
	"call_status": "180"
}
```

---
title: SIP Request
subtitle: 'Sends a SIP INFO, NOTIFY, or MESSAGE request during a call.'
---

```json
{
  "verb": "sip:request",
  "method": "INFO",
  "headers": {
    "X-Metadata": "my sip metadata"
  }
  "actionHook": "/info"
}
```

## Parameters

<ParamField path="method" type="string" required={true}>
  SIP method, should be one of `INFO`, `MESSAGE`, or `NOTIFY`.
</ParamField>

<ParamField path="actionHook" type="string" required={false}>
  A webhook to call when the SIP request has completed.
</ParamField>

<ParamField path="body" type="string" required={false}>
  The body of the SIP request, if any.
</ParamField>

<ParamField path="headers" type="object" required={false}>
  An object containing headers (key-value pairs) to include with the SIP request.
</ParamField>

The sip:request verb completes when a response is received from the far end. 

### actionHook properties

The actionHook provides the status code of the sip response:

- `result`: 'success' or 'failed'
- `sipStatus`: sip status code of response
- `err`: error message, in the case of failure

