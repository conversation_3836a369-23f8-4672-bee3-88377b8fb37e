---
title: Dial
subtitle: >-
  Create a new call by dialing out to a telephone number, a registered sip user,
  a sip uri, or a Microsoft Teams user.
---

```json maxLines=100
{
  "verb": "dial",
  "actionHook": "/outdial",
  "callerId": "+161755511234",
  "answerOnBridge": true,
  "dtmfCapture": ["*2", "*3"],
  "dtmfHook": {
    "url": "/dtmf",
    "method": "GET"
  },
  "amd": {
    "actionHook": "/answeringMachineDetection",
    "disconnectOnAMD": true,
  },
  "target": [
    {
      "type": "phone",
      "number": "+15085551000",
      "trunk": "Twilio"
    },
    {
      "type": "sip",
      "sipUri": "sip:<EMAIL>",
      "auth": {
        "username": "foo",
        "password": "bar"
      },
      "proxy" : "sip:*********:5060"
    },
    {
      "type": "user",
      "name": "<EMAIL>"
    }
  ]
}
```
As the example above illustrates, when you execute the dial command you are making one or more outbound call attempts in an effort to create one new call, which is bridged to a parent call. The `target` property specifies an array of call destinations that will be attempted simultaneously.  

If multiple endpoints are specified in the `target` array, all targets are outdialed at the same time (e.g., "simring", or "blast outdial" as some folks call it) and the call will be connected to the first endpoint that answers the call (or, optionally, the first call that completes a call screening application as specified in the `confirmHook` property).

There are several types of endpoints that you can dial:

* a telephone phone number -- this call attempt will be sent out through your configured Carrier,
* a webrtc or sip client that has registered directly with your subdomain,
* a sip endpoint, identified by a sip uri (and possibly authentication parameters), or
* Microsoft Teams user

## Parameters

<ParamField path="target" type="array" required={true}>
  array of up to 10 [destinations](#target-types) to simultaneously dial.  
  The first person (or entity) to answer the call will be connected to the caller, and the rest of the called numbers will be hung up.
</ParamField>

<ParamField path="actionHook" type="string" required={false}>
  webhook to invoke when the call ends. The webhook will include [properties](#actionhook-properties) describing the outcome of the call attempt.
</ParamField>

<ParamField path="amd" type="object" required={false}>
  enable answering machine detection; see [answering machine detection](/guides/features/answering-machine-detection) for details.
</ParamField>

<ParamField path="anchorMedia" type="boolean" required={false}>
  if true, jambonz will not release the media from feature server for the bridged call. [See here](/guides/features/managing-media-anchors) for details.  
  Default: false.
</ParamField>

<ParamField path="answerOnBridge" type="boolean" required={false}>
  If set to true, the inbound call will ring until the number that was dialed answers the call, and at that point a 200 OK will be sent on the inbound leg.  
  If false, the inbound call will be answered immediately as the outbound call is placed.  
  Defaults to false.
</ParamField>

<ParamField path="boostAudioSignal" type="string | number" required={false}>
  A string or integer value indicating the number of decibels to boost or reduce the strength of the outgoing audio signal to the caller/called party, e.g. "-6 dB". Note this applies to the main track only, not to any [dub](/verbs/verbs/dub) tracks.
</ParamField>

<ParamField path="callerId" type="string" required={false}>
  The inbound caller's phone number, which is displayed to the number that was dialed.  
  The caller ID must be a valid E.164 number.  
  Defaults to caller ID on inbound call.
</ParamField>

<ParamField path="confirmHook" type="string" required={false}>
  webhook for an application to run on the callee's end after the dialed number answers but before the call is connected.  
  This allows the caller to provide information to the dialed number, giving them the opportunity to decline the call before they answer.  
  Note that if you want to run different applications on specific destinations, you can specify the 'url' property on the nested [target](#h5-target-types) object.
</ParamField>

<ParamField path="dialMusic" type="string" required={false}>
  url that specifies a .wav or .mp3 audio file of custom audio or ringback to play to the caller while the outbound call is ringing.
</ParamField>

<ParamField path="dtmfCapture" type="array" required={false}>
  an array of strings that represent DTMF sequences which, when detected, will trigger a mid-call notification to the application via the configured `dtmfHook`.
</ParamField>

<ParamField path="dtmfHook" type="string" required={false}>
  a webhook to call when a `dtmfCapture` entry is matched.  
  This is a notification only -- no response is expected, and any desired actions must be carried out via the REST updateCall API.
</ParamField>

<ParamField path="dub" type="object" required={false}>
  a nested [dub](/verbs/verbs/dub) verb to add additional audio tracks into the outbound call.
</ParamField>

<ParamField path="exitMediaPath" type="boolean" required={false}>
  if true, jambonz will attempt to re-invite itself completely out of the media path for the call; [see below](#exitmediapath) for details.  
  Defaults to false.
</ParamField>

<ParamField path="headers" type="object" required={false}>
  an object containing arbitrary SIP headers to apply to the outbound call attempt(s).
</ParamField>

<ParamField path="listen" type="object" required={false}>
  a nested [listen](/verbs/verbs/listen) action, which will cause audio from the call to be streamed to a remote server over a websocket connection.
</ParamField>

<ParamField path="referHook" type="string" required={false}>
  webhook to invoke when an incoming SIP REFER is received on a dialed call.  
  If the application wishes to accept and process the REFER, the webhook application should return an HTTP status code 200 with no body, and jambonz will send a SIP 202 Accepted.  
  Otherwise, any HTTP non-success status will cause jambonz to send a SIP response to the REFER with the same status code.  
  
  Note that jambonz will send the 202 Accepted and do nothing further. It is the responsibility of the third-party application to outdial a new call and bridge the other leg, presumably by using the REST API.  
  See [this example app](https://github.com/jambonz/sip-blind-transfer) for more details.
</ParamField>

<ParamField path="timeLimit" type="number" required={false}>
  max length of call in seconds.
</ParamField>

<ParamField path="timeout" type="number" required={false}>
  ring no answer timeout, in seconds.  
  Defaults to 60.
</ParamField>

<ParamField path="transcribe" type="object" required={false}>
  a nested [transcribe](/verbs/verbs/transcribe) action, which will cause the call to be transcribed.
</ParamField>

##### target types

<AccordionGroup>
  <Accordion title="phone">
    Sends the call to a telephone number using one of your provisioned Carriers. The following properties are available:
    | option        | description | required  |
    | ------------- |-------------| -----|
    | type | must be "phone" | yes |
    | confirmHook | A webhook for an application to run on the callee's end after the dialed number answers but before the call is connected. This will override the confirmHook property set on the parent dial verb, if any.| no |
    | number | a telephone number in E.164 number. | yes |
    | trunk | if provided, this should be the name of a Carrier that you created in the jambonz portal or API, which you want to use to complete this call.  If not provided, jambonz will selectone of your configured Carriers that has an outbound trunk. | no |
  </Accordion>

  <Accordion title="sip">
    Sends the call to a sip uri. If the sip trunking provider enforces username/password authentication, supply the credentials in the `auth` property.
    The following properties are available:
    | option        | description | required  |
    | ------------- |-------------| -----|
    | type | must be "sip" | yes |
    | confirmHook | A webhook for an application to run on the callee's end after the dialed number answers but before the call is connected. This will override the confirmHook property set on the parent dial verb, if any.| no |
    | sipUri | sip uri to send call to | yes |
    | auth | authentication credentials | no |
    | auth.username | sip username | no |
    | auth.password | sip password | no |
  </Accordion>

  <Accordion title="user">
    Sends the call to a registered webrtc or sip user. The following properties are available:
    | option        | description | required  |
    | ------------- |-------------| -----|
    | type | must be "user" | yes |
    | confirmHook | A webhook for an application to run on the callee's end after the dialed number answers but before the call is connected. This will override the confirmHook property set on the parent dial verb, if any.| no |
    | name | registered sip user, including domain (e.g. ""<EMAIL>") | yes |
  </Accordion>

  <Accordion title="teams">
    If Microsoft Teams integration has been configured, you can dial out to Teams users. The following properties are available:
    | option        | description | required  |
    | ------------- |-------------| -----|
    | type | must be "teams" | yes |
    | tenant | Microsoft Teams customer tenant domain name.  Will default to the Microsoft Teams tenant associated with the account of the calling party. | no |
    | number | the phone number that has been mapped to the teams user by the Microsoft Teams administrator | yes |
    | voicemail | if true, dial directly into user's voicemail to leave a message | no |
  </Accordion>
</AccordionGroup>

The `confirmHook` property that can be optionally specified as part of the target types is a web callback that will be invoked when the outdial call is answered.  That callback should return an application that will run on the outbound call before bridging it to the inbound call.  If the application completes with the outbound call still in a stable/connected state, then the two calls will be bridged together.

<Note>
The confirmHook allows you to easily implement call screening applications (e.g. "You have a call from so-and-so.  Press 1 to decline").
</Note>

#### actionHook properties

The actionHook that is invoked when the dial command ends will include the following properties:

| property name  | description | 
| ------------- |-------------|
| dial_call_sid | the unique call_sid identifier for the child call |
| dial_status | the final status of the call attempt, one of 'completed', 'failed', 'busy', 'no-answer', or 'queued'|
| dial_sip_status | the sip status of the final response to the INVITE that was sent|

#### amd.hook properties

This webhook that is invoked when amd property is included and jambonz has either determined the type of called party (human or machine) or has detected a tone or beep.

| property name  | description | 
| ------------- |-------------|
| event | one of 'amd', 'beep', or 'silence' |
| amd_type| 'human' or 'machine', only provided when event = 'amd'|

#### exitMediaPath

The purpose of the `exitMediaPath` is to support use cases where it is important that the media path not touch the jambonz system at all.  The common use case is the need to transfer a call to a human agent or credit card system where the caller will be giving their credit card details over the phone.  In order to have a PCI-Compliant transaction it is necessary that this conversation not be able to be recorded, stored, or in any way reach the jambonz system.  Performing the `dial` verb using the `exitMediaPath` property ensures this happens.

For more details on controlling the media path during a call, please see [Controlling media path during call](/guides/features/managing-media-anchors).

#### proxy

If you set a `proxy` value on the target then jambonz will use this as a SIP Proxy for the outgoing INVITE, for example `sip:***********:5060`
