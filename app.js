require('dotenv').config()
const express = require('express');
const app = express();
const {createServer} = require('http');
const {createEndpoint} = require('@jambonz/node-client-ws');
const server = createServer(app);
const makeService = createEndpoint({server});
// Erweiterte Logging-Konfiguration mit täglichen Dateien und Zeitstempel
const path = require('path');
const fs = require('fs');

// Log-Verzeichnis sicherstellen
const logDir = path.join(__dirname, 'logs');
if (!fs.existsSync(logDir)) {
  fs.mkdirSync(logDir, { recursive: true });
}

// Aktuelles Datum für Log-Datei
const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD Format
const logFile = path.join(logDir, `jasz-ai-${today}.json`);

const logger = require('pino')({
  level: process.env.LOGLEVEL || 'info',
  timestamp: () => `,"timestamp":"${new Date().toISOString()}"`,
  formatters: {
    level: (label) => {
      return { level: label };
    },
    log: (object) => {
      // Strukturiere die Log-Objekte besser
      return {
        ...object,
        service: 'jasz-ai-voice-agent',
        environment: process.env.NODE_ENV || 'development'
      };
    }
  },
  serializers: {
    req: (req) => ({
      method: req.method,
      url: req.url,
      headers: req.headers,
      body: req.body
    }),
    res: (res) => ({
      statusCode: res.statusCode,
      headers: res.headers
    }),
    err: (err) => ({
      type: err.constructor.name,
      message: err.message,
      stack: err.stack
    })
  }
}, require('pino').destination({
  dest: logFile,
  sync: false
}));

// WICHTIG: Console.log zu Pino umleiten für vollständige Logs
const originalConsole = {
  log: console.log,
  info: console.info,
  warn: console.warn,
  error: console.error
};

// Console-Methoden zu Pino umleiten
console.log = (...args) => {
  logger.info(args.join(' '));
  // Auch weiterhin im Terminal anzeigen in Development
  if (process.env.NODE_ENV !== 'production') {
    originalConsole.log(...args);
  }
};

console.info = (...args) => {
  logger.info(args.join(' '));
  if (process.env.NODE_ENV !== 'production') {
    originalConsole.info(...args);
  }
};

console.warn = (...args) => {
  logger.warn(args.join(' '));
  if (process.env.NODE_ENV !== 'production') {
    originalConsole.warn(...args);
  }
};

console.error = (...args) => {
  logger.error(args.join(' '));
  if (process.env.NODE_ENV !== 'production') {
    originalConsole.error(...args);
  }
};

const port = process.env.WS_PORT || 3000;
const routes = require('./lib/api');

app.locals = {
  ...app.locals,
  logger
};

app.use(express.urlencoded({ extended: true }));
app.use(express.json());
app.use('/api', (req, res, next) => {
  next();
},routes);

// Mock-Routen entfernt - echte Implementation in /lib/api/tools.js

require('./lib/routes')({logger, makeService});

app.post('/final', (req, res) => {
  logger.info({
    event: 'webhook_final',
    endpoint: '/final',
    request_body: req.body,
    timestamp: new Date().toISOString()
  }, 'Final webhook received');

  res.status(200).send();
});

app.post('/event', (req, res) => {
  logger.info({
    event: 'webhook_event',
    endpoint: '/event',
    request_body: req.body,
    timestamp: new Date().toISOString()
  }, 'Event webhook received');

  res.status(200).send();
});

server.listen(port, () => {
  logger.info(`jambonz websocket server listening at http://localhost:${port}`);
});
