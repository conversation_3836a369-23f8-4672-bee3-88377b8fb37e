#!/bin/bash

echo "=== JASZ-AI Freeswitch App Starter ==="

# Wechsle ins Projektverzeichnis
cd /home/<USER>/JASZ-AI-Freeswitch

# Stoppe alle laufenden Node.js-Prozesse für app.js
echo "Stoppe laufende Node.js-Prozesse..."
pkill -f "node app.js" 2>/dev/null || true

# <PERSON><PERSON> kurz, damit Prozesse sauber beendet werden
sleep 2

# Terminal leeren
clear

# Umgebungsvariable setzen
export WS_PORT=3002

echo "Starte JASZ-AI Freeswitch App auf Port 3002..."
echo "Drücke Ctrl+C zum Beenden"
echo "=========================================="

# Überprüfe ob node_modules existiert
if [ ! -d "node_modules" ]; then
    echo "WARNUNG: node_modules nicht gefunden. Führe 'npm install' aus."
    exit 1
fi

# App starten
node app.js
