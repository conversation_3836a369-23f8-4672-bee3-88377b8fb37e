{"name": "ultravox-s2s-example", "version": "0.0.1", "description": "example jambonz websocket application using Ultravox's Realtime API", "main": "app.js", "scripts": {"start": "node app", "jslint": "eslint app.js lib --fix"}, "author": "<PERSON>", "license": "MIT", "dependencies": {"@jambonz/node-client": "^0.3.92", "@jambonz/node-client-ws": "^0.1.79", "axios": "^1.9.0", "dotenv": "^16.5.0", "express": "^4.21.2", "fuzzball": "^2.2.2", "pg": "^8.16.0", "pino": "^9.7.0", "soundex": "^0.2.1"}, "devDependencies": {"eslint": "^9.12.0", "eslint-plugin-promise": "^7.1.0"}}